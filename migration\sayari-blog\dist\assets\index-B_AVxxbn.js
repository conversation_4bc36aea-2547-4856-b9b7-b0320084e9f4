(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))l(a);new MutationObserver(a=>{for(const c of a)if(c.type==="childList")for(const f of c.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function i(a){const c={};return a.integrity&&(c.integrity=a.integrity),a.referrerPolicy&&(c.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?c.credentials="include":a.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function l(a){if(a.ep)return;a.ep=!0;const c=i(a);fetch(a.href,c)}})();function Bp(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o}function Wp(o){if(Object.prototype.hasOwnProperty.call(o,"__esModule"))return o;var r=o.default;if(typeof r=="function"){var i=function l(){var a=!1;try{a=this instanceof l}catch{}return a?Reflect.construct(r,arguments,this.constructor):r.apply(this,arguments)};i.prototype=r.prototype}else i={};return Object.defineProperty(i,"__esModule",{value:!0}),Object.keys(o).forEach(function(l){var a=Object.getOwnPropertyDescriptor(o,l);Object.defineProperty(i,l,a.get?a:{enumerable:!0,get:function(){return o[l]}})}),i}var ql={exports:{}},di={},Vl={exports:{}},ie={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sd;function Hp(){if(Sd)return ie;Sd=1;var o=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),w=Symbol.iterator;function S(x){return x===null||typeof x!="object"?null:(x=w&&x[w]||x["@@iterator"],typeof x=="function"?x:null)}var j={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},O=Object.assign,$={};function E(x,A,ne){this.props=x,this.context=A,this.refs=$,this.updater=ne||j}E.prototype.isReactComponent={},E.prototype.setState=function(x,A){if(typeof x!="object"&&typeof x!="function"&&x!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,x,A,"setState")},E.prototype.forceUpdate=function(x){this.updater.enqueueForceUpdate(this,x,"forceUpdate")};function N(){}N.prototype=E.prototype;function J(x,A,ne){this.props=x,this.context=A,this.refs=$,this.updater=ne||j}var D=J.prototype=new N;D.constructor=J,O(D,E.prototype),D.isPureReactComponent=!0;var W=Array.isArray,G=Object.prototype.hasOwnProperty,ee={current:null},re={key:!0,ref:!0,__self:!0,__source:!0};function be(x,A,ne){var se,ae={},ue=null,he=null;if(A!=null)for(se in A.ref!==void 0&&(he=A.ref),A.key!==void 0&&(ue=""+A.key),A)G.call(A,se)&&!re.hasOwnProperty(se)&&(ae[se]=A[se]);var de=arguments.length-2;if(de===1)ae.children=ne;else if(1<de){for(var ye=Array(de),rt=0;rt<de;rt++)ye[rt]=arguments[rt+2];ae.children=ye}if(x&&x.defaultProps)for(se in de=x.defaultProps,de)ae[se]===void 0&&(ae[se]=de[se]);return{$$typeof:o,type:x,key:ue,ref:he,props:ae,_owner:ee.current}}function je(x,A){return{$$typeof:o,type:x.type,key:A,ref:x.ref,props:x.props,_owner:x._owner}}function ze(x){return typeof x=="object"&&x!==null&&x.$$typeof===o}function Ct(x){var A={"=":"=0",":":"=2"};return"$"+x.replace(/[=:]/g,function(ne){return A[ne]})}var Pt=/\/+/g;function nt(x,A){return typeof x=="object"&&x!==null&&x.key!=null?Ct(""+x.key):A.toString(36)}function mt(x,A,ne,se,ae){var ue=typeof x;(ue==="undefined"||ue==="boolean")&&(x=null);var he=!1;if(x===null)he=!0;else switch(ue){case"string":case"number":he=!0;break;case"object":switch(x.$$typeof){case o:case r:he=!0}}if(he)return he=x,ae=ae(he),x=se===""?"."+nt(he,0):se,W(ae)?(ne="",x!=null&&(ne=x.replace(Pt,"$&/")+"/"),mt(ae,A,ne,"",function(rt){return rt})):ae!=null&&(ze(ae)&&(ae=je(ae,ne+(!ae.key||he&&he.key===ae.key?"":(""+ae.key).replace(Pt,"$&/")+"/")+x)),A.push(ae)),1;if(he=0,se=se===""?".":se+":",W(x))for(var de=0;de<x.length;de++){ue=x[de];var ye=se+nt(ue,de);he+=mt(ue,A,ne,ye,ae)}else if(ye=S(x),typeof ye=="function")for(x=ye.call(x),de=0;!(ue=x.next()).done;)ue=ue.value,ye=se+nt(ue,de++),he+=mt(ue,A,ne,ye,ae);else if(ue==="object")throw A=String(x),Error("Objects are not valid as a React child (found: "+(A==="[object Object]"?"object with keys {"+Object.keys(x).join(", ")+"}":A)+"). If you meant to render a collection of children, use an array instead.");return he}function jt(x,A,ne){if(x==null)return x;var se=[],ae=0;return mt(x,se,"","",function(ue){return A.call(ne,ue,ae++)}),se}function Ke(x){if(x._status===-1){var A=x._result;A=A(),A.then(function(ne){(x._status===0||x._status===-1)&&(x._status=1,x._result=ne)},function(ne){(x._status===0||x._status===-1)&&(x._status=2,x._result=ne)}),x._status===-1&&(x._status=0,x._result=A)}if(x._status===1)return x._result.default;throw x._result}var Ee={current:null},M={transition:null},X={ReactCurrentDispatcher:Ee,ReactCurrentBatchConfig:M,ReactCurrentOwner:ee};function H(){throw Error("act(...) is not supported in production builds of React.")}return ie.Children={map:jt,forEach:function(x,A,ne){jt(x,function(){A.apply(this,arguments)},ne)},count:function(x){var A=0;return jt(x,function(){A++}),A},toArray:function(x){return jt(x,function(A){return A})||[]},only:function(x){if(!ze(x))throw Error("React.Children.only expected to receive a single React element child.");return x}},ie.Component=E,ie.Fragment=i,ie.Profiler=a,ie.PureComponent=J,ie.StrictMode=l,ie.Suspense=p,ie.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=X,ie.act=H,ie.cloneElement=function(x,A,ne){if(x==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+x+".");var se=O({},x.props),ae=x.key,ue=x.ref,he=x._owner;if(A!=null){if(A.ref!==void 0&&(ue=A.ref,he=ee.current),A.key!==void 0&&(ae=""+A.key),x.type&&x.type.defaultProps)var de=x.type.defaultProps;for(ye in A)G.call(A,ye)&&!re.hasOwnProperty(ye)&&(se[ye]=A[ye]===void 0&&de!==void 0?de[ye]:A[ye])}var ye=arguments.length-2;if(ye===1)se.children=ne;else if(1<ye){de=Array(ye);for(var rt=0;rt<ye;rt++)de[rt]=arguments[rt+2];se.children=de}return{$$typeof:o,type:x.type,key:ae,ref:ue,props:se,_owner:he}},ie.createContext=function(x){return x={$$typeof:f,_currentValue:x,_currentValue2:x,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},x.Provider={$$typeof:c,_context:x},x.Consumer=x},ie.createElement=be,ie.createFactory=function(x){var A=be.bind(null,x);return A.type=x,A},ie.createRef=function(){return{current:null}},ie.forwardRef=function(x){return{$$typeof:h,render:x}},ie.isValidElement=ze,ie.lazy=function(x){return{$$typeof:v,_payload:{_status:-1,_result:x},_init:Ke}},ie.memo=function(x,A){return{$$typeof:g,type:x,compare:A===void 0?null:A}},ie.startTransition=function(x){var A=M.transition;M.transition={};try{x()}finally{M.transition=A}},ie.unstable_act=H,ie.useCallback=function(x,A){return Ee.current.useCallback(x,A)},ie.useContext=function(x){return Ee.current.useContext(x)},ie.useDebugValue=function(){},ie.useDeferredValue=function(x){return Ee.current.useDeferredValue(x)},ie.useEffect=function(x,A){return Ee.current.useEffect(x,A)},ie.useId=function(){return Ee.current.useId()},ie.useImperativeHandle=function(x,A,ne){return Ee.current.useImperativeHandle(x,A,ne)},ie.useInsertionEffect=function(x,A){return Ee.current.useInsertionEffect(x,A)},ie.useLayoutEffect=function(x,A){return Ee.current.useLayoutEffect(x,A)},ie.useMemo=function(x,A){return Ee.current.useMemo(x,A)},ie.useReducer=function(x,A,ne){return Ee.current.useReducer(x,A,ne)},ie.useRef=function(x){return Ee.current.useRef(x)},ie.useState=function(x){return Ee.current.useState(x)},ie.useSyncExternalStore=function(x,A,ne){return Ee.current.useSyncExternalStore(x,A,ne)},ie.useTransition=function(){return Ee.current.useTransition()},ie.version="18.3.1",ie}var Ed;function va(){return Ed||(Ed=1,Vl.exports=Hp()),Vl.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xd;function qp(){if(xd)return di;xd=1;var o=va(),r=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,a=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function f(h,p,g){var v,w={},S=null,j=null;g!==void 0&&(S=""+g),p.key!==void 0&&(S=""+p.key),p.ref!==void 0&&(j=p.ref);for(v in p)l.call(p,v)&&!c.hasOwnProperty(v)&&(w[v]=p[v]);if(h&&h.defaultProps)for(v in p=h.defaultProps,p)w[v]===void 0&&(w[v]=p[v]);return{$$typeof:r,type:h,key:S,ref:j,props:w,_owner:a.current}}return di.Fragment=i,di.jsx=f,di.jsxs=f,di}var Cd;function Vp(){return Cd||(Cd=1,ql.exports=qp()),ql.exports}var L=Vp(),R=va(),bs={},Kl={exports:{}},Ze={},Jl={exports:{}},Gl={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pd;function Kp(){return Pd||(Pd=1,function(o){function r(M,X){var H=M.length;M.push(X);e:for(;0<H;){var x=H-1>>>1,A=M[x];if(0<a(A,X))M[x]=X,M[H]=A,H=x;else break e}}function i(M){return M.length===0?null:M[0]}function l(M){if(M.length===0)return null;var X=M[0],H=M.pop();if(H!==X){M[0]=H;e:for(var x=0,A=M.length,ne=A>>>1;x<ne;){var se=2*(x+1)-1,ae=M[se],ue=se+1,he=M[ue];if(0>a(ae,H))ue<A&&0>a(he,ae)?(M[x]=he,M[ue]=H,x=ue):(M[x]=ae,M[se]=H,x=se);else if(ue<A&&0>a(he,H))M[x]=he,M[ue]=H,x=ue;else break e}}return X}function a(M,X){var H=M.sortIndex-X.sortIndex;return H!==0?H:M.id-X.id}if(typeof performance=="object"&&typeof performance.now=="function"){var c=performance;o.unstable_now=function(){return c.now()}}else{var f=Date,h=f.now();o.unstable_now=function(){return f.now()-h}}var p=[],g=[],v=1,w=null,S=3,j=!1,O=!1,$=!1,E=typeof setTimeout=="function"?setTimeout:null,N=typeof clearTimeout=="function"?clearTimeout:null,J=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function D(M){for(var X=i(g);X!==null;){if(X.callback===null)l(g);else if(X.startTime<=M)l(g),X.sortIndex=X.expirationTime,r(p,X);else break;X=i(g)}}function W(M){if($=!1,D(M),!O)if(i(p)!==null)O=!0,Ke(G);else{var X=i(g);X!==null&&Ee(W,X.startTime-M)}}function G(M,X){O=!1,$&&($=!1,N(be),be=-1),j=!0;var H=S;try{for(D(X),w=i(p);w!==null&&(!(w.expirationTime>X)||M&&!Ct());){var x=w.callback;if(typeof x=="function"){w.callback=null,S=w.priorityLevel;var A=x(w.expirationTime<=X);X=o.unstable_now(),typeof A=="function"?w.callback=A:w===i(p)&&l(p),D(X)}else l(p);w=i(p)}if(w!==null)var ne=!0;else{var se=i(g);se!==null&&Ee(W,se.startTime-X),ne=!1}return ne}finally{w=null,S=H,j=!1}}var ee=!1,re=null,be=-1,je=5,ze=-1;function Ct(){return!(o.unstable_now()-ze<je)}function Pt(){if(re!==null){var M=o.unstable_now();ze=M;var X=!0;try{X=re(!0,M)}finally{X?nt():(ee=!1,re=null)}}else ee=!1}var nt;if(typeof J=="function")nt=function(){J(Pt)};else if(typeof MessageChannel<"u"){var mt=new MessageChannel,jt=mt.port2;mt.port1.onmessage=Pt,nt=function(){jt.postMessage(null)}}else nt=function(){E(Pt,0)};function Ke(M){re=M,ee||(ee=!0,nt())}function Ee(M,X){be=E(function(){M(o.unstable_now())},X)}o.unstable_IdlePriority=5,o.unstable_ImmediatePriority=1,o.unstable_LowPriority=4,o.unstable_NormalPriority=3,o.unstable_Profiling=null,o.unstable_UserBlockingPriority=2,o.unstable_cancelCallback=function(M){M.callback=null},o.unstable_continueExecution=function(){O||j||(O=!0,Ke(G))},o.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):je=0<M?Math.floor(1e3/M):5},o.unstable_getCurrentPriorityLevel=function(){return S},o.unstable_getFirstCallbackNode=function(){return i(p)},o.unstable_next=function(M){switch(S){case 1:case 2:case 3:var X=3;break;default:X=S}var H=S;S=X;try{return M()}finally{S=H}},o.unstable_pauseExecution=function(){},o.unstable_requestPaint=function(){},o.unstable_runWithPriority=function(M,X){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var H=S;S=M;try{return X()}finally{S=H}},o.unstable_scheduleCallback=function(M,X,H){var x=o.unstable_now();switch(typeof H=="object"&&H!==null?(H=H.delay,H=typeof H=="number"&&0<H?x+H:x):H=x,M){case 1:var A=-1;break;case 2:A=250;break;case 5:A=**********;break;case 4:A=1e4;break;default:A=5e3}return A=H+A,M={id:v++,callback:X,priorityLevel:M,startTime:H,expirationTime:A,sortIndex:-1},H>x?(M.sortIndex=H,r(g,M),i(p)===null&&M===i(g)&&($?(N(be),be=-1):$=!0,Ee(W,H-x))):(M.sortIndex=A,r(p,M),O||j||(O=!0,Ke(G))),M},o.unstable_shouldYield=Ct,o.unstable_wrapCallback=function(M){var X=S;return function(){var H=S;S=X;try{return M.apply(this,arguments)}finally{S=H}}}}(Gl)),Gl}var jd;function Jp(){return jd||(jd=1,Jl.exports=Kp()),Jl.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Td;function Gp(){if(Td)return Ze;Td=1;var o=va(),r=Jp();function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,a={};function c(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(a[e]=t,e=0;e<t.length;e++)l.add(t[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),p=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,v={},w={};function S(e){return p.call(w,e)?!0:p.call(v,e)?!1:g.test(e)?w[e]=!0:(v[e]=!0,!1)}function j(e,t,n,s){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return s?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function O(e,t,n,s){if(t===null||typeof t>"u"||j(e,t,n,s))return!0;if(s)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function $(e,t,n,s,u,d,m){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=s,this.attributeNamespace=u,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=d,this.removeEmptyString=m}var E={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){E[e]=new $(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];E[t]=new $(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){E[e]=new $(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){E[e]=new $(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){E[e]=new $(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){E[e]=new $(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){E[e]=new $(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){E[e]=new $(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){E[e]=new $(e,5,!1,e.toLowerCase(),null,!1,!1)});var N=/[\-:]([a-z])/g;function J(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(N,J);E[t]=new $(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(N,J);E[t]=new $(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(N,J);E[t]=new $(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){E[e]=new $(e,1,!1,e.toLowerCase(),null,!1,!1)}),E.xlinkHref=new $("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){E[e]=new $(e,1,!1,e.toLowerCase(),null,!0,!0)});function D(e,t,n,s){var u=E.hasOwnProperty(t)?E[t]:null;(u!==null?u.type!==0:s||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(O(t,n,u,s)&&(n=null),s||u===null?S(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):u.mustUseProperty?e[u.propertyName]=n===null?u.type===3?!1:"":n:(t=u.attributeName,s=u.attributeNamespace,n===null?e.removeAttribute(t):(u=u.type,n=u===3||u===4&&n===!0?"":""+n,s?e.setAttributeNS(s,t,n):e.setAttribute(t,n))))}var W=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,G=Symbol.for("react.element"),ee=Symbol.for("react.portal"),re=Symbol.for("react.fragment"),be=Symbol.for("react.strict_mode"),je=Symbol.for("react.profiler"),ze=Symbol.for("react.provider"),Ct=Symbol.for("react.context"),Pt=Symbol.for("react.forward_ref"),nt=Symbol.for("react.suspense"),mt=Symbol.for("react.suspense_list"),jt=Symbol.for("react.memo"),Ke=Symbol.for("react.lazy"),Ee=Symbol.for("react.offscreen"),M=Symbol.iterator;function X(e){return e===null||typeof e!="object"?null:(e=M&&e[M]||e["@@iterator"],typeof e=="function"?e:null)}var H=Object.assign,x;function A(e){if(x===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);x=t&&t[1]||""}return`
`+x+e}var ne=!1;function se(e,t){if(!e||ne)return"";ne=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(T){var s=T}Reflect.construct(e,[],t)}else{try{t.call()}catch(T){s=T}e.call(t.prototype)}else{try{throw Error()}catch(T){s=T}e()}}catch(T){if(T&&s&&typeof T.stack=="string"){for(var u=T.stack.split(`
`),d=s.stack.split(`
`),m=u.length-1,y=d.length-1;1<=m&&0<=y&&u[m]!==d[y];)y--;for(;1<=m&&0<=y;m--,y--)if(u[m]!==d[y]){if(m!==1||y!==1)do if(m--,y--,0>y||u[m]!==d[y]){var _=`
`+u[m].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),_}while(1<=m&&0<=y);break}}}finally{ne=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?A(e):""}function ae(e){switch(e.tag){case 5:return A(e.type);case 16:return A("Lazy");case 13:return A("Suspense");case 19:return A("SuspenseList");case 0:case 2:case 15:return e=se(e.type,!1),e;case 11:return e=se(e.type.render,!1),e;case 1:return e=se(e.type,!0),e;default:return""}}function ue(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case re:return"Fragment";case ee:return"Portal";case je:return"Profiler";case be:return"StrictMode";case nt:return"Suspense";case mt:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ct:return(e.displayName||"Context")+".Consumer";case ze:return(e._context.displayName||"Context")+".Provider";case Pt:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case jt:return t=e.displayName||null,t!==null?t:ue(e.type)||"Memo";case Ke:t=e._payload,e=e._init;try{return ue(e(t))}catch{}}return null}function he(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ue(t);case 8:return t===be?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function de(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ye(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function rt(e){var t=ye(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,d=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(m){s=""+m,d.call(this,m)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return s},setValue:function(m){s=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Pi(e){e._valueTracker||(e._valueTracker=rt(e))}function Ta(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),s="";return e&&(s=ye(e)?e.checked?"true":"false":e.value),e=s,e!==n?(t.setValue(e),!0):!1}function ji(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ys(e,t){var n=t.checked;return H({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ra(e,t){var n=t.defaultValue==null?"":t.defaultValue,s=t.checked!=null?t.checked:t.defaultChecked;n=de(t.value!=null?t.value:n),e._wrapperState={initialChecked:s,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Oa(e,t){t=t.checked,t!=null&&D(e,"checked",t,!1)}function Xs(e,t){Oa(e,t);var n=de(t.value),s=t.type;if(n!=null)s==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Zs(e,t.type,n):t.hasOwnProperty("defaultValue")&&Zs(e,t.type,de(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Na(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var s=t.type;if(!(s!=="submit"&&s!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Zs(e,t,n){(t!=="number"||ji(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Pr=Array.isArray;function zn(e,t,n,s){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&s&&(e[n].defaultSelected=!0)}else{for(n=""+de(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,s&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function eo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(i(91));return H({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function La(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(i(92));if(Pr(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:de(n)}}function $a(e,t){var n=de(t.value),s=de(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),s!=null&&(e.defaultValue=""+s)}function Ia(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Aa(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function to(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Aa(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ti,ba=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,s,u){MSApp.execUnsafeLocalFunction(function(){return e(t,n,s,u)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ti=Ti||document.createElement("div"),Ti.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ti.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function jr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Tr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Vf=["Webkit","ms","Moz","O"];Object.keys(Tr).forEach(function(e){Vf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Tr[t]=Tr[e]})});function Da(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Tr.hasOwnProperty(e)&&Tr[e]?(""+t).trim():t+"px"}function Ua(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var s=n.indexOf("--")===0,u=Da(n,t[n],s);n==="float"&&(n="cssFloat"),s?e.setProperty(n,u):e[n]=u}}var Kf=H({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function no(e,t){if(t){if(Kf[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(i(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(i(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(t.style!=null&&typeof t.style!="object")throw Error(i(62))}}function ro(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var io=null;function so(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var oo=null,Mn=null,Fn=null;function za(e){if(e=Qr(e)){if(typeof oo!="function")throw Error(i(280));var t=e.stateNode;t&&(t=Yi(t),oo(e.stateNode,e.type,t))}}function Ma(e){Mn?Fn?Fn.push(e):Fn=[e]:Mn=e}function Fa(){if(Mn){var e=Mn,t=Fn;if(Fn=Mn=null,za(e),t)for(e=0;e<t.length;e++)za(t[e])}}function Ba(e,t){return e(t)}function Wa(){}var lo=!1;function Ha(e,t,n){if(lo)return e(t,n);lo=!0;try{return Ba(e,t,n)}finally{lo=!1,(Mn!==null||Fn!==null)&&(Wa(),Fa())}}function Rr(e,t){var n=e.stateNode;if(n===null)return null;var s=Yi(n);if(s===null)return null;n=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(i(231,t,typeof n));return n}var ao=!1;if(h)try{var Or={};Object.defineProperty(Or,"passive",{get:function(){ao=!0}}),window.addEventListener("test",Or,Or),window.removeEventListener("test",Or,Or)}catch{ao=!1}function Jf(e,t,n,s,u,d,m,y,_){var T=Array.prototype.slice.call(arguments,3);try{t.apply(n,T)}catch(b){this.onError(b)}}var Nr=!1,Ri=null,Oi=!1,uo=null,Gf={onError:function(e){Nr=!0,Ri=e}};function Qf(e,t,n,s,u,d,m,y,_){Nr=!1,Ri=null,Jf.apply(Gf,arguments)}function Yf(e,t,n,s,u,d,m,y,_){if(Qf.apply(this,arguments),Nr){if(Nr){var T=Ri;Nr=!1,Ri=null}else throw Error(i(198));Oi||(Oi=!0,uo=T)}}function _n(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function qa(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Va(e){if(_n(e)!==e)throw Error(i(188))}function Xf(e){var t=e.alternate;if(!t){if(t=_n(e),t===null)throw Error(i(188));return t!==e?null:e}for(var n=e,s=t;;){var u=n.return;if(u===null)break;var d=u.alternate;if(d===null){if(s=u.return,s!==null){n=s;continue}break}if(u.child===d.child){for(d=u.child;d;){if(d===n)return Va(u),e;if(d===s)return Va(u),t;d=d.sibling}throw Error(i(188))}if(n.return!==s.return)n=u,s=d;else{for(var m=!1,y=u.child;y;){if(y===n){m=!0,n=u,s=d;break}if(y===s){m=!0,s=u,n=d;break}y=y.sibling}if(!m){for(y=d.child;y;){if(y===n){m=!0,n=d,s=u;break}if(y===s){m=!0,s=d,n=u;break}y=y.sibling}if(!m)throw Error(i(189))}}if(n.alternate!==s)throw Error(i(190))}if(n.tag!==3)throw Error(i(188));return n.stateNode.current===n?e:t}function Ka(e){return e=Xf(e),e!==null?Ja(e):null}function Ja(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ja(e);if(t!==null)return t;e=e.sibling}return null}var Ga=r.unstable_scheduleCallback,Qa=r.unstable_cancelCallback,Zf=r.unstable_shouldYield,eh=r.unstable_requestPaint,Ce=r.unstable_now,th=r.unstable_getCurrentPriorityLevel,co=r.unstable_ImmediatePriority,Ya=r.unstable_UserBlockingPriority,Ni=r.unstable_NormalPriority,nh=r.unstable_LowPriority,Xa=r.unstable_IdlePriority,Li=null,Tt=null;function rh(e){if(Tt&&typeof Tt.onCommitFiberRoot=="function")try{Tt.onCommitFiberRoot(Li,e,void 0,(e.current.flags&128)===128)}catch{}}var gt=Math.clz32?Math.clz32:oh,ih=Math.log,sh=Math.LN2;function oh(e){return e>>>=0,e===0?32:31-(ih(e)/sh|0)|0}var $i=64,Ii=4194304;function Lr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ai(e,t){var n=e.pendingLanes;if(n===0)return 0;var s=0,u=e.suspendedLanes,d=e.pingedLanes,m=n&268435455;if(m!==0){var y=m&~u;y!==0?s=Lr(y):(d&=m,d!==0&&(s=Lr(d)))}else m=n&~u,m!==0?s=Lr(m):d!==0&&(s=Lr(d));if(s===0)return 0;if(t!==0&&t!==s&&(t&u)===0&&(u=s&-s,d=t&-t,u>=d||u===16&&(d&4194240)!==0))return t;if((s&4)!==0&&(s|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=s;0<t;)n=31-gt(t),u=1<<n,s|=e[n],t&=~u;return s}function lh(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ah(e,t){for(var n=e.suspendedLanes,s=e.pingedLanes,u=e.expirationTimes,d=e.pendingLanes;0<d;){var m=31-gt(d),y=1<<m,_=u[m];_===-1?((y&n)===0||(y&s)!==0)&&(u[m]=lh(y,t)):_<=t&&(e.expiredLanes|=y),d&=~y}}function fo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Za(){var e=$i;return $i<<=1,($i&4194240)===0&&($i=64),e}function ho(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function $r(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-gt(t),e[t]=n}function uh(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<n;){var u=31-gt(n),d=1<<u;t[u]=0,s[u]=-1,e[u]=-1,n&=~d}}function po(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var s=31-gt(n),u=1<<s;u&t|e[s]&t&&(e[s]|=t),n&=~u}}var fe=0;function eu(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var tu,mo,nu,ru,iu,go=!1,bi=[],Gt=null,Qt=null,Yt=null,Ir=new Map,Ar=new Map,Xt=[],ch="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function su(e,t){switch(e){case"focusin":case"focusout":Gt=null;break;case"dragenter":case"dragleave":Qt=null;break;case"mouseover":case"mouseout":Yt=null;break;case"pointerover":case"pointerout":Ir.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ar.delete(t.pointerId)}}function br(e,t,n,s,u,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:n,eventSystemFlags:s,nativeEvent:d,targetContainers:[u]},t!==null&&(t=Qr(t),t!==null&&mo(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function dh(e,t,n,s,u){switch(t){case"focusin":return Gt=br(Gt,e,t,n,s,u),!0;case"dragenter":return Qt=br(Qt,e,t,n,s,u),!0;case"mouseover":return Yt=br(Yt,e,t,n,s,u),!0;case"pointerover":var d=u.pointerId;return Ir.set(d,br(Ir.get(d)||null,e,t,n,s,u)),!0;case"gotpointercapture":return d=u.pointerId,Ar.set(d,br(Ar.get(d)||null,e,t,n,s,u)),!0}return!1}function ou(e){var t=kn(e.target);if(t!==null){var n=_n(t);if(n!==null){if(t=n.tag,t===13){if(t=qa(n),t!==null){e.blockedOn=t,iu(e.priority,function(){nu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Di(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=yo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var s=new n.constructor(n.type,n);io=s,n.target.dispatchEvent(s),io=null}else return t=Qr(n),t!==null&&mo(t),e.blockedOn=n,!1;t.shift()}return!0}function lu(e,t,n){Di(e)&&n.delete(t)}function fh(){go=!1,Gt!==null&&Di(Gt)&&(Gt=null),Qt!==null&&Di(Qt)&&(Qt=null),Yt!==null&&Di(Yt)&&(Yt=null),Ir.forEach(lu),Ar.forEach(lu)}function Dr(e,t){e.blockedOn===t&&(e.blockedOn=null,go||(go=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,fh)))}function Ur(e){function t(u){return Dr(u,e)}if(0<bi.length){Dr(bi[0],e);for(var n=1;n<bi.length;n++){var s=bi[n];s.blockedOn===e&&(s.blockedOn=null)}}for(Gt!==null&&Dr(Gt,e),Qt!==null&&Dr(Qt,e),Yt!==null&&Dr(Yt,e),Ir.forEach(t),Ar.forEach(t),n=0;n<Xt.length;n++)s=Xt[n],s.blockedOn===e&&(s.blockedOn=null);for(;0<Xt.length&&(n=Xt[0],n.blockedOn===null);)ou(n),n.blockedOn===null&&Xt.shift()}var Bn=W.ReactCurrentBatchConfig,Ui=!0;function hh(e,t,n,s){var u=fe,d=Bn.transition;Bn.transition=null;try{fe=1,vo(e,t,n,s)}finally{fe=u,Bn.transition=d}}function ph(e,t,n,s){var u=fe,d=Bn.transition;Bn.transition=null;try{fe=4,vo(e,t,n,s)}finally{fe=u,Bn.transition=d}}function vo(e,t,n,s){if(Ui){var u=yo(e,t,n,s);if(u===null)Ao(e,t,s,zi,n),su(e,s);else if(dh(u,e,t,n,s))s.stopPropagation();else if(su(e,s),t&4&&-1<ch.indexOf(e)){for(;u!==null;){var d=Qr(u);if(d!==null&&tu(d),d=yo(e,t,n,s),d===null&&Ao(e,t,s,zi,n),d===u)break;u=d}u!==null&&s.stopPropagation()}else Ao(e,t,s,null,n)}}var zi=null;function yo(e,t,n,s){if(zi=null,e=so(s),e=kn(e),e!==null)if(t=_n(e),t===null)e=null;else if(n=t.tag,n===13){if(e=qa(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return zi=e,null}function au(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(th()){case co:return 1;case Ya:return 4;case Ni:case nh:return 16;case Xa:return 536870912;default:return 16}default:return 16}}var Zt=null,wo=null,Mi=null;function uu(){if(Mi)return Mi;var e,t=wo,n=t.length,s,u="value"in Zt?Zt.value:Zt.textContent,d=u.length;for(e=0;e<n&&t[e]===u[e];e++);var m=n-e;for(s=1;s<=m&&t[n-s]===u[d-s];s++);return Mi=u.slice(e,1<s?1-s:void 0)}function Fi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Bi(){return!0}function cu(){return!1}function it(e){function t(n,s,u,d,m){this._reactName=n,this._targetInst=u,this.type=s,this.nativeEvent=d,this.target=m,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(n=e[y],this[y]=n?n(d):d[y]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Bi:cu,this.isPropagationStopped=cu,this}return H(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Bi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Bi)},persist:function(){},isPersistent:Bi}),t}var Wn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_o=it(Wn),zr=H({},Wn,{view:0,detail:0}),mh=it(zr),ko,So,Mr,Wi=H({},zr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:xo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Mr&&(Mr&&e.type==="mousemove"?(ko=e.screenX-Mr.screenX,So=e.screenY-Mr.screenY):So=ko=0,Mr=e),ko)},movementY:function(e){return"movementY"in e?e.movementY:So}}),du=it(Wi),gh=H({},Wi,{dataTransfer:0}),vh=it(gh),yh=H({},zr,{relatedTarget:0}),Eo=it(yh),wh=H({},Wn,{animationName:0,elapsedTime:0,pseudoElement:0}),_h=it(wh),kh=H({},Wn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Sh=it(kh),Eh=H({},Wn,{data:0}),fu=it(Eh),xh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ch={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ph={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ph[e])?!!t[e]:!1}function xo(){return jh}var Th=H({},zr,{key:function(e){if(e.key){var t=xh[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ch[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:xo,charCode:function(e){return e.type==="keypress"?Fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Rh=it(Th),Oh=H({},Wi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),hu=it(Oh),Nh=H({},zr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:xo}),Lh=it(Nh),$h=H({},Wn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ih=it($h),Ah=H({},Wi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),bh=it(Ah),Dh=[9,13,27,32],Co=h&&"CompositionEvent"in window,Fr=null;h&&"documentMode"in document&&(Fr=document.documentMode);var Uh=h&&"TextEvent"in window&&!Fr,pu=h&&(!Co||Fr&&8<Fr&&11>=Fr),mu=" ",gu=!1;function vu(e,t){switch(e){case"keyup":return Dh.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function yu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Hn=!1;function zh(e,t){switch(e){case"compositionend":return yu(t);case"keypress":return t.which!==32?null:(gu=!0,mu);case"textInput":return e=t.data,e===mu&&gu?null:e;default:return null}}function Mh(e,t){if(Hn)return e==="compositionend"||!Co&&vu(e,t)?(e=uu(),Mi=wo=Zt=null,Hn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return pu&&t.locale!=="ko"?null:t.data;default:return null}}var Fh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function wu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Fh[e.type]:t==="textarea"}function _u(e,t,n,s){Ma(s),t=Ji(t,"onChange"),0<t.length&&(n=new _o("onChange","change",null,n,s),e.push({event:n,listeners:t}))}var Br=null,Wr=null;function Bh(e){Uu(e,0)}function Hi(e){var t=Gn(e);if(Ta(t))return e}function Wh(e,t){if(e==="change")return t}var ku=!1;if(h){var Po;if(h){var jo="oninput"in document;if(!jo){var Su=document.createElement("div");Su.setAttribute("oninput","return;"),jo=typeof Su.oninput=="function"}Po=jo}else Po=!1;ku=Po&&(!document.documentMode||9<document.documentMode)}function Eu(){Br&&(Br.detachEvent("onpropertychange",xu),Wr=Br=null)}function xu(e){if(e.propertyName==="value"&&Hi(Wr)){var t=[];_u(t,Wr,e,so(e)),Ha(Bh,t)}}function Hh(e,t,n){e==="focusin"?(Eu(),Br=t,Wr=n,Br.attachEvent("onpropertychange",xu)):e==="focusout"&&Eu()}function qh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Hi(Wr)}function Vh(e,t){if(e==="click")return Hi(t)}function Kh(e,t){if(e==="input"||e==="change")return Hi(t)}function Jh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var vt=typeof Object.is=="function"?Object.is:Jh;function Hr(e,t){if(vt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),s=Object.keys(t);if(n.length!==s.length)return!1;for(s=0;s<n.length;s++){var u=n[s];if(!p.call(t,u)||!vt(e[u],t[u]))return!1}return!0}function Cu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Pu(e,t){var n=Cu(e);e=0;for(var s;n;){if(n.nodeType===3){if(s=e+n.textContent.length,e<=t&&s>=t)return{node:n,offset:t-e};e=s}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Cu(n)}}function ju(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ju(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Tu(){for(var e=window,t=ji();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ji(e.document)}return t}function To(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Gh(e){var t=Tu(),n=e.focusedElem,s=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ju(n.ownerDocument.documentElement,n)){if(s!==null&&To(n)){if(t=s.start,e=s.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var u=n.textContent.length,d=Math.min(s.start,u);s=s.end===void 0?d:Math.min(s.end,u),!e.extend&&d>s&&(u=s,s=d,d=u),u=Pu(n,d);var m=Pu(n,s);u&&m&&(e.rangeCount!==1||e.anchorNode!==u.node||e.anchorOffset!==u.offset||e.focusNode!==m.node||e.focusOffset!==m.offset)&&(t=t.createRange(),t.setStart(u.node,u.offset),e.removeAllRanges(),d>s?(e.addRange(t),e.extend(m.node,m.offset)):(t.setEnd(m.node,m.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Qh=h&&"documentMode"in document&&11>=document.documentMode,qn=null,Ro=null,qr=null,Oo=!1;function Ru(e,t,n){var s=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Oo||qn==null||qn!==ji(s)||(s=qn,"selectionStart"in s&&To(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),qr&&Hr(qr,s)||(qr=s,s=Ji(Ro,"onSelect"),0<s.length&&(t=new _o("onSelect","select",null,t,n),e.push({event:t,listeners:s}),t.target=qn)))}function qi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Vn={animationend:qi("Animation","AnimationEnd"),animationiteration:qi("Animation","AnimationIteration"),animationstart:qi("Animation","AnimationStart"),transitionend:qi("Transition","TransitionEnd")},No={},Ou={};h&&(Ou=document.createElement("div").style,"AnimationEvent"in window||(delete Vn.animationend.animation,delete Vn.animationiteration.animation,delete Vn.animationstart.animation),"TransitionEvent"in window||delete Vn.transitionend.transition);function Vi(e){if(No[e])return No[e];if(!Vn[e])return e;var t=Vn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ou)return No[e]=t[n];return e}var Nu=Vi("animationend"),Lu=Vi("animationiteration"),$u=Vi("animationstart"),Iu=Vi("transitionend"),Au=new Map,bu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function en(e,t){Au.set(e,t),c(t,[e])}for(var Lo=0;Lo<bu.length;Lo++){var $o=bu[Lo],Yh=$o.toLowerCase(),Xh=$o[0].toUpperCase()+$o.slice(1);en(Yh,"on"+Xh)}en(Nu,"onAnimationEnd"),en(Lu,"onAnimationIteration"),en($u,"onAnimationStart"),en("dblclick","onDoubleClick"),en("focusin","onFocus"),en("focusout","onBlur"),en(Iu,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),c("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),c("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),c("onBeforeInput",["compositionend","keypress","textInput","paste"]),c("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Vr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Zh=new Set("cancel close invalid load scroll toggle".split(" ").concat(Vr));function Du(e,t,n){var s=e.type||"unknown-event";e.currentTarget=n,Yf(s,t,void 0,e),e.currentTarget=null}function Uu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var s=e[n],u=s.event;s=s.listeners;e:{var d=void 0;if(t)for(var m=s.length-1;0<=m;m--){var y=s[m],_=y.instance,T=y.currentTarget;if(y=y.listener,_!==d&&u.isPropagationStopped())break e;Du(u,y,T),d=_}else for(m=0;m<s.length;m++){if(y=s[m],_=y.instance,T=y.currentTarget,y=y.listener,_!==d&&u.isPropagationStopped())break e;Du(u,y,T),d=_}}}if(Oi)throw e=uo,Oi=!1,uo=null,e}function ge(e,t){var n=t[Fo];n===void 0&&(n=t[Fo]=new Set);var s=e+"__bubble";n.has(s)||(zu(t,e,2,!1),n.add(s))}function Io(e,t,n){var s=0;t&&(s|=4),zu(n,e,s,t)}var Ki="_reactListening"+Math.random().toString(36).slice(2);function Kr(e){if(!e[Ki]){e[Ki]=!0,l.forEach(function(n){n!=="selectionchange"&&(Zh.has(n)||Io(n,!1,e),Io(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ki]||(t[Ki]=!0,Io("selectionchange",!1,t))}}function zu(e,t,n,s){switch(au(t)){case 1:var u=hh;break;case 4:u=ph;break;default:u=vo}n=u.bind(null,t,n,e),u=void 0,!ao||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),s?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function Ao(e,t,n,s,u){var d=s;if((t&1)===0&&(t&2)===0&&s!==null)e:for(;;){if(s===null)return;var m=s.tag;if(m===3||m===4){var y=s.stateNode.containerInfo;if(y===u||y.nodeType===8&&y.parentNode===u)break;if(m===4)for(m=s.return;m!==null;){var _=m.tag;if((_===3||_===4)&&(_=m.stateNode.containerInfo,_===u||_.nodeType===8&&_.parentNode===u))return;m=m.return}for(;y!==null;){if(m=kn(y),m===null)return;if(_=m.tag,_===5||_===6){s=d=m;continue e}y=y.parentNode}}s=s.return}Ha(function(){var T=d,b=so(n),U=[];e:{var I=Au.get(e);if(I!==void 0){var F=_o,q=e;switch(e){case"keypress":if(Fi(n)===0)break e;case"keydown":case"keyup":F=Rh;break;case"focusin":q="focus",F=Eo;break;case"focusout":q="blur",F=Eo;break;case"beforeblur":case"afterblur":F=Eo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":F=du;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":F=vh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":F=Lh;break;case Nu:case Lu:case $u:F=_h;break;case Iu:F=Ih;break;case"scroll":F=mh;break;case"wheel":F=bh;break;case"copy":case"cut":case"paste":F=Sh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":F=hu}var V=(t&4)!==0,Pe=!V&&e==="scroll",C=V?I!==null?I+"Capture":null:I;V=[];for(var k=T,P;k!==null;){P=k;var z=P.stateNode;if(P.tag===5&&z!==null&&(P=z,C!==null&&(z=Rr(k,C),z!=null&&V.push(Jr(k,z,P)))),Pe)break;k=k.return}0<V.length&&(I=new F(I,q,null,n,b),U.push({event:I,listeners:V}))}}if((t&7)===0){e:{if(I=e==="mouseover"||e==="pointerover",F=e==="mouseout"||e==="pointerout",I&&n!==io&&(q=n.relatedTarget||n.fromElement)&&(kn(q)||q[bt]))break e;if((F||I)&&(I=b.window===b?b:(I=b.ownerDocument)?I.defaultView||I.parentWindow:window,F?(q=n.relatedTarget||n.toElement,F=T,q=q?kn(q):null,q!==null&&(Pe=_n(q),q!==Pe||q.tag!==5&&q.tag!==6)&&(q=null)):(F=null,q=T),F!==q)){if(V=du,z="onMouseLeave",C="onMouseEnter",k="mouse",(e==="pointerout"||e==="pointerover")&&(V=hu,z="onPointerLeave",C="onPointerEnter",k="pointer"),Pe=F==null?I:Gn(F),P=q==null?I:Gn(q),I=new V(z,k+"leave",F,n,b),I.target=Pe,I.relatedTarget=P,z=null,kn(b)===T&&(V=new V(C,k+"enter",q,n,b),V.target=P,V.relatedTarget=Pe,z=V),Pe=z,F&&q)t:{for(V=F,C=q,k=0,P=V;P;P=Kn(P))k++;for(P=0,z=C;z;z=Kn(z))P++;for(;0<k-P;)V=Kn(V),k--;for(;0<P-k;)C=Kn(C),P--;for(;k--;){if(V===C||C!==null&&V===C.alternate)break t;V=Kn(V),C=Kn(C)}V=null}else V=null;F!==null&&Mu(U,I,F,V,!1),q!==null&&Pe!==null&&Mu(U,Pe,q,V,!0)}}e:{if(I=T?Gn(T):window,F=I.nodeName&&I.nodeName.toLowerCase(),F==="select"||F==="input"&&I.type==="file")var K=Wh;else if(wu(I))if(ku)K=Kh;else{K=qh;var Q=Hh}else(F=I.nodeName)&&F.toLowerCase()==="input"&&(I.type==="checkbox"||I.type==="radio")&&(K=Vh);if(K&&(K=K(e,T))){_u(U,K,n,b);break e}Q&&Q(e,I,T),e==="focusout"&&(Q=I._wrapperState)&&Q.controlled&&I.type==="number"&&Zs(I,"number",I.value)}switch(Q=T?Gn(T):window,e){case"focusin":(wu(Q)||Q.contentEditable==="true")&&(qn=Q,Ro=T,qr=null);break;case"focusout":qr=Ro=qn=null;break;case"mousedown":Oo=!0;break;case"contextmenu":case"mouseup":case"dragend":Oo=!1,Ru(U,n,b);break;case"selectionchange":if(Qh)break;case"keydown":case"keyup":Ru(U,n,b)}var Y;if(Co)e:{switch(e){case"compositionstart":var Z="onCompositionStart";break e;case"compositionend":Z="onCompositionEnd";break e;case"compositionupdate":Z="onCompositionUpdate";break e}Z=void 0}else Hn?vu(e,n)&&(Z="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(Z="onCompositionStart");Z&&(pu&&n.locale!=="ko"&&(Hn||Z!=="onCompositionStart"?Z==="onCompositionEnd"&&Hn&&(Y=uu()):(Zt=b,wo="value"in Zt?Zt.value:Zt.textContent,Hn=!0)),Q=Ji(T,Z),0<Q.length&&(Z=new fu(Z,e,null,n,b),U.push({event:Z,listeners:Q}),Y?Z.data=Y:(Y=yu(n),Y!==null&&(Z.data=Y)))),(Y=Uh?zh(e,n):Mh(e,n))&&(T=Ji(T,"onBeforeInput"),0<T.length&&(b=new fu("onBeforeInput","beforeinput",null,n,b),U.push({event:b,listeners:T}),b.data=Y))}Uu(U,t)})}function Jr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ji(e,t){for(var n=t+"Capture",s=[];e!==null;){var u=e,d=u.stateNode;u.tag===5&&d!==null&&(u=d,d=Rr(e,n),d!=null&&s.unshift(Jr(e,d,u)),d=Rr(e,t),d!=null&&s.push(Jr(e,d,u))),e=e.return}return s}function Kn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Mu(e,t,n,s,u){for(var d=t._reactName,m=[];n!==null&&n!==s;){var y=n,_=y.alternate,T=y.stateNode;if(_!==null&&_===s)break;y.tag===5&&T!==null&&(y=T,u?(_=Rr(n,d),_!=null&&m.unshift(Jr(n,_,y))):u||(_=Rr(n,d),_!=null&&m.push(Jr(n,_,y)))),n=n.return}m.length!==0&&e.push({event:t,listeners:m})}var ep=/\r\n?/g,tp=/\u0000|\uFFFD/g;function Fu(e){return(typeof e=="string"?e:""+e).replace(ep,`
`).replace(tp,"")}function Gi(e,t,n){if(t=Fu(t),Fu(e)!==t&&n)throw Error(i(425))}function Qi(){}var bo=null,Do=null;function Uo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var zo=typeof setTimeout=="function"?setTimeout:void 0,np=typeof clearTimeout=="function"?clearTimeout:void 0,Bu=typeof Promise=="function"?Promise:void 0,rp=typeof queueMicrotask=="function"?queueMicrotask:typeof Bu<"u"?function(e){return Bu.resolve(null).then(e).catch(ip)}:zo;function ip(e){setTimeout(function(){throw e})}function Mo(e,t){var n=t,s=0;do{var u=n.nextSibling;if(e.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(s===0){e.removeChild(u),Ur(t);return}s--}else n!=="$"&&n!=="$?"&&n!=="$!"||s++;n=u}while(n);Ur(t)}function tn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Wu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Jn=Math.random().toString(36).slice(2),Rt="__reactFiber$"+Jn,Gr="__reactProps$"+Jn,bt="__reactContainer$"+Jn,Fo="__reactEvents$"+Jn,sp="__reactListeners$"+Jn,op="__reactHandles$"+Jn;function kn(e){var t=e[Rt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[bt]||n[Rt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Wu(e);e!==null;){if(n=e[Rt])return n;e=Wu(e)}return t}e=n,n=e.parentNode}return null}function Qr(e){return e=e[Rt]||e[bt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(i(33))}function Yi(e){return e[Gr]||null}var Bo=[],Qn=-1;function nn(e){return{current:e}}function ve(e){0>Qn||(e.current=Bo[Qn],Bo[Qn]=null,Qn--)}function pe(e,t){Qn++,Bo[Qn]=e.current,e.current=t}var rn={},Me=nn(rn),Je=nn(!1),Sn=rn;function Yn(e,t){var n=e.type.contextTypes;if(!n)return rn;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===t)return s.__reactInternalMemoizedMaskedChildContext;var u={},d;for(d in n)u[d]=t[d];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=u),u}function Ge(e){return e=e.childContextTypes,e!=null}function Xi(){ve(Je),ve(Me)}function Hu(e,t,n){if(Me.current!==rn)throw Error(i(168));pe(Me,t),pe(Je,n)}function qu(e,t,n){var s=e.stateNode;if(t=t.childContextTypes,typeof s.getChildContext!="function")return n;s=s.getChildContext();for(var u in s)if(!(u in t))throw Error(i(108,he(e)||"Unknown",u));return H({},n,s)}function Zi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||rn,Sn=Me.current,pe(Me,e),pe(Je,Je.current),!0}function Vu(e,t,n){var s=e.stateNode;if(!s)throw Error(i(169));n?(e=qu(e,t,Sn),s.__reactInternalMemoizedMergedChildContext=e,ve(Je),ve(Me),pe(Me,e)):ve(Je),pe(Je,n)}var Dt=null,es=!1,Wo=!1;function Ku(e){Dt===null?Dt=[e]:Dt.push(e)}function lp(e){es=!0,Ku(e)}function sn(){if(!Wo&&Dt!==null){Wo=!0;var e=0,t=fe;try{var n=Dt;for(fe=1;e<n.length;e++){var s=n[e];do s=s(!0);while(s!==null)}Dt=null,es=!1}catch(u){throw Dt!==null&&(Dt=Dt.slice(e+1)),Ga(co,sn),u}finally{fe=t,Wo=!1}}return null}var Xn=[],Zn=0,ts=null,ns=0,ut=[],ct=0,En=null,Ut=1,zt="";function xn(e,t){Xn[Zn++]=ns,Xn[Zn++]=ts,ts=e,ns=t}function Ju(e,t,n){ut[ct++]=Ut,ut[ct++]=zt,ut[ct++]=En,En=e;var s=Ut;e=zt;var u=32-gt(s)-1;s&=~(1<<u),n+=1;var d=32-gt(t)+u;if(30<d){var m=u-u%5;d=(s&(1<<m)-1).toString(32),s>>=m,u-=m,Ut=1<<32-gt(t)+u|n<<u|s,zt=d+e}else Ut=1<<d|n<<u|s,zt=e}function Ho(e){e.return!==null&&(xn(e,1),Ju(e,1,0))}function qo(e){for(;e===ts;)ts=Xn[--Zn],Xn[Zn]=null,ns=Xn[--Zn],Xn[Zn]=null;for(;e===En;)En=ut[--ct],ut[ct]=null,zt=ut[--ct],ut[ct]=null,Ut=ut[--ct],ut[ct]=null}var st=null,ot=null,we=!1,yt=null;function Gu(e,t){var n=pt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Qu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,st=e,ot=tn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,st=e,ot=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=En!==null?{id:Ut,overflow:zt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=pt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,st=e,ot=null,!0):!1;default:return!1}}function Vo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ko(e){if(we){var t=ot;if(t){var n=t;if(!Qu(e,t)){if(Vo(e))throw Error(i(418));t=tn(n.nextSibling);var s=st;t&&Qu(e,t)?Gu(s,n):(e.flags=e.flags&-4097|2,we=!1,st=e)}}else{if(Vo(e))throw Error(i(418));e.flags=e.flags&-4097|2,we=!1,st=e}}}function Yu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;st=e}function rs(e){if(e!==st)return!1;if(!we)return Yu(e),we=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Uo(e.type,e.memoizedProps)),t&&(t=ot)){if(Vo(e))throw Xu(),Error(i(418));for(;t;)Gu(e,t),t=tn(t.nextSibling)}if(Yu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ot=tn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ot=null}}else ot=st?tn(e.stateNode.nextSibling):null;return!0}function Xu(){for(var e=ot;e;)e=tn(e.nextSibling)}function er(){ot=st=null,we=!1}function Jo(e){yt===null?yt=[e]:yt.push(e)}var ap=W.ReactCurrentBatchConfig;function Yr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(i(309));var s=n.stateNode}if(!s)throw Error(i(147,e));var u=s,d=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d?t.ref:(t=function(m){var y=u.refs;m===null?delete y[d]:y[d]=m},t._stringRef=d,t)}if(typeof e!="string")throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function is(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Zu(e){var t=e._init;return t(e._payload)}function ec(e){function t(C,k){if(e){var P=C.deletions;P===null?(C.deletions=[k],C.flags|=16):P.push(k)}}function n(C,k){if(!e)return null;for(;k!==null;)t(C,k),k=k.sibling;return null}function s(C,k){for(C=new Map;k!==null;)k.key!==null?C.set(k.key,k):C.set(k.index,k),k=k.sibling;return C}function u(C,k){return C=hn(C,k),C.index=0,C.sibling=null,C}function d(C,k,P){return C.index=P,e?(P=C.alternate,P!==null?(P=P.index,P<k?(C.flags|=2,k):P):(C.flags|=2,k)):(C.flags|=1048576,k)}function m(C){return e&&C.alternate===null&&(C.flags|=2),C}function y(C,k,P,z){return k===null||k.tag!==6?(k=zl(P,C.mode,z),k.return=C,k):(k=u(k,P),k.return=C,k)}function _(C,k,P,z){var K=P.type;return K===re?b(C,k,P.props.children,z,P.key):k!==null&&(k.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===Ke&&Zu(K)===k.type)?(z=u(k,P.props),z.ref=Yr(C,k,P),z.return=C,z):(z=Ts(P.type,P.key,P.props,null,C.mode,z),z.ref=Yr(C,k,P),z.return=C,z)}function T(C,k,P,z){return k===null||k.tag!==4||k.stateNode.containerInfo!==P.containerInfo||k.stateNode.implementation!==P.implementation?(k=Ml(P,C.mode,z),k.return=C,k):(k=u(k,P.children||[]),k.return=C,k)}function b(C,k,P,z,K){return k===null||k.tag!==7?(k=Ln(P,C.mode,z,K),k.return=C,k):(k=u(k,P),k.return=C,k)}function U(C,k,P){if(typeof k=="string"&&k!==""||typeof k=="number")return k=zl(""+k,C.mode,P),k.return=C,k;if(typeof k=="object"&&k!==null){switch(k.$$typeof){case G:return P=Ts(k.type,k.key,k.props,null,C.mode,P),P.ref=Yr(C,null,k),P.return=C,P;case ee:return k=Ml(k,C.mode,P),k.return=C,k;case Ke:var z=k._init;return U(C,z(k._payload),P)}if(Pr(k)||X(k))return k=Ln(k,C.mode,P,null),k.return=C,k;is(C,k)}return null}function I(C,k,P,z){var K=k!==null?k.key:null;if(typeof P=="string"&&P!==""||typeof P=="number")return K!==null?null:y(C,k,""+P,z);if(typeof P=="object"&&P!==null){switch(P.$$typeof){case G:return P.key===K?_(C,k,P,z):null;case ee:return P.key===K?T(C,k,P,z):null;case Ke:return K=P._init,I(C,k,K(P._payload),z)}if(Pr(P)||X(P))return K!==null?null:b(C,k,P,z,null);is(C,P)}return null}function F(C,k,P,z,K){if(typeof z=="string"&&z!==""||typeof z=="number")return C=C.get(P)||null,y(k,C,""+z,K);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case G:return C=C.get(z.key===null?P:z.key)||null,_(k,C,z,K);case ee:return C=C.get(z.key===null?P:z.key)||null,T(k,C,z,K);case Ke:var Q=z._init;return F(C,k,P,Q(z._payload),K)}if(Pr(z)||X(z))return C=C.get(P)||null,b(k,C,z,K,null);is(k,z)}return null}function q(C,k,P,z){for(var K=null,Q=null,Y=k,Z=k=0,$e=null;Y!==null&&Z<P.length;Z++){Y.index>Z?($e=Y,Y=null):$e=Y.sibling;var ce=I(C,Y,P[Z],z);if(ce===null){Y===null&&(Y=$e);break}e&&Y&&ce.alternate===null&&t(C,Y),k=d(ce,k,Z),Q===null?K=ce:Q.sibling=ce,Q=ce,Y=$e}if(Z===P.length)return n(C,Y),we&&xn(C,Z),K;if(Y===null){for(;Z<P.length;Z++)Y=U(C,P[Z],z),Y!==null&&(k=d(Y,k,Z),Q===null?K=Y:Q.sibling=Y,Q=Y);return we&&xn(C,Z),K}for(Y=s(C,Y);Z<P.length;Z++)$e=F(Y,C,Z,P[Z],z),$e!==null&&(e&&$e.alternate!==null&&Y.delete($e.key===null?Z:$e.key),k=d($e,k,Z),Q===null?K=$e:Q.sibling=$e,Q=$e);return e&&Y.forEach(function(pn){return t(C,pn)}),we&&xn(C,Z),K}function V(C,k,P,z){var K=X(P);if(typeof K!="function")throw Error(i(150));if(P=K.call(P),P==null)throw Error(i(151));for(var Q=K=null,Y=k,Z=k=0,$e=null,ce=P.next();Y!==null&&!ce.done;Z++,ce=P.next()){Y.index>Z?($e=Y,Y=null):$e=Y.sibling;var pn=I(C,Y,ce.value,z);if(pn===null){Y===null&&(Y=$e);break}e&&Y&&pn.alternate===null&&t(C,Y),k=d(pn,k,Z),Q===null?K=pn:Q.sibling=pn,Q=pn,Y=$e}if(ce.done)return n(C,Y),we&&xn(C,Z),K;if(Y===null){for(;!ce.done;Z++,ce=P.next())ce=U(C,ce.value,z),ce!==null&&(k=d(ce,k,Z),Q===null?K=ce:Q.sibling=ce,Q=ce);return we&&xn(C,Z),K}for(Y=s(C,Y);!ce.done;Z++,ce=P.next())ce=F(Y,C,Z,ce.value,z),ce!==null&&(e&&ce.alternate!==null&&Y.delete(ce.key===null?Z:ce.key),k=d(ce,k,Z),Q===null?K=ce:Q.sibling=ce,Q=ce);return e&&Y.forEach(function(Fp){return t(C,Fp)}),we&&xn(C,Z),K}function Pe(C,k,P,z){if(typeof P=="object"&&P!==null&&P.type===re&&P.key===null&&(P=P.props.children),typeof P=="object"&&P!==null){switch(P.$$typeof){case G:e:{for(var K=P.key,Q=k;Q!==null;){if(Q.key===K){if(K=P.type,K===re){if(Q.tag===7){n(C,Q.sibling),k=u(Q,P.props.children),k.return=C,C=k;break e}}else if(Q.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===Ke&&Zu(K)===Q.type){n(C,Q.sibling),k=u(Q,P.props),k.ref=Yr(C,Q,P),k.return=C,C=k;break e}n(C,Q);break}else t(C,Q);Q=Q.sibling}P.type===re?(k=Ln(P.props.children,C.mode,z,P.key),k.return=C,C=k):(z=Ts(P.type,P.key,P.props,null,C.mode,z),z.ref=Yr(C,k,P),z.return=C,C=z)}return m(C);case ee:e:{for(Q=P.key;k!==null;){if(k.key===Q)if(k.tag===4&&k.stateNode.containerInfo===P.containerInfo&&k.stateNode.implementation===P.implementation){n(C,k.sibling),k=u(k,P.children||[]),k.return=C,C=k;break e}else{n(C,k);break}else t(C,k);k=k.sibling}k=Ml(P,C.mode,z),k.return=C,C=k}return m(C);case Ke:return Q=P._init,Pe(C,k,Q(P._payload),z)}if(Pr(P))return q(C,k,P,z);if(X(P))return V(C,k,P,z);is(C,P)}return typeof P=="string"&&P!==""||typeof P=="number"?(P=""+P,k!==null&&k.tag===6?(n(C,k.sibling),k=u(k,P),k.return=C,C=k):(n(C,k),k=zl(P,C.mode,z),k.return=C,C=k),m(C)):n(C,k)}return Pe}var tr=ec(!0),tc=ec(!1),ss=nn(null),os=null,nr=null,Go=null;function Qo(){Go=nr=os=null}function Yo(e){var t=ss.current;ve(ss),e._currentValue=t}function Xo(e,t,n){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===n)break;e=e.return}}function rr(e,t){os=e,Go=nr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(Qe=!0),e.firstContext=null)}function dt(e){var t=e._currentValue;if(Go!==e)if(e={context:e,memoizedValue:t,next:null},nr===null){if(os===null)throw Error(i(308));nr=e,os.dependencies={lanes:0,firstContext:e}}else nr=nr.next=e;return t}var Cn=null;function Zo(e){Cn===null?Cn=[e]:Cn.push(e)}function nc(e,t,n,s){var u=t.interleaved;return u===null?(n.next=n,Zo(t)):(n.next=u.next,u.next=n),t.interleaved=n,Mt(e,s)}function Mt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var on=!1;function el(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function rc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ft(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ln(e,t,n){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,(le&2)!==0){var u=s.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),s.pending=t,Mt(e,n)}return u=s.interleaved,u===null?(t.next=t,Zo(s)):(t.next=u.next,u.next=t),s.interleaved=t,Mt(e,n)}function ls(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,po(e,n)}}function ic(e,t){var n=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,n===s)){var u=null,d=null;if(n=n.firstBaseUpdate,n!==null){do{var m={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};d===null?u=d=m:d=d.next=m,n=n.next}while(n!==null);d===null?u=d=t:d=d.next=t}else u=d=t;n={baseState:s.baseState,firstBaseUpdate:u,lastBaseUpdate:d,shared:s.shared,effects:s.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function as(e,t,n,s){var u=e.updateQueue;on=!1;var d=u.firstBaseUpdate,m=u.lastBaseUpdate,y=u.shared.pending;if(y!==null){u.shared.pending=null;var _=y,T=_.next;_.next=null,m===null?d=T:m.next=T,m=_;var b=e.alternate;b!==null&&(b=b.updateQueue,y=b.lastBaseUpdate,y!==m&&(y===null?b.firstBaseUpdate=T:y.next=T,b.lastBaseUpdate=_))}if(d!==null){var U=u.baseState;m=0,b=T=_=null,y=d;do{var I=y.lane,F=y.eventTime;if((s&I)===I){b!==null&&(b=b.next={eventTime:F,lane:0,tag:y.tag,payload:y.payload,callback:y.callback,next:null});e:{var q=e,V=y;switch(I=t,F=n,V.tag){case 1:if(q=V.payload,typeof q=="function"){U=q.call(F,U,I);break e}U=q;break e;case 3:q.flags=q.flags&-65537|128;case 0:if(q=V.payload,I=typeof q=="function"?q.call(F,U,I):q,I==null)break e;U=H({},U,I);break e;case 2:on=!0}}y.callback!==null&&y.lane!==0&&(e.flags|=64,I=u.effects,I===null?u.effects=[y]:I.push(y))}else F={eventTime:F,lane:I,tag:y.tag,payload:y.payload,callback:y.callback,next:null},b===null?(T=b=F,_=U):b=b.next=F,m|=I;if(y=y.next,y===null){if(y=u.shared.pending,y===null)break;I=y,y=I.next,I.next=null,u.lastBaseUpdate=I,u.shared.pending=null}}while(!0);if(b===null&&(_=U),u.baseState=_,u.firstBaseUpdate=T,u.lastBaseUpdate=b,t=u.shared.interleaved,t!==null){u=t;do m|=u.lane,u=u.next;while(u!==t)}else d===null&&(u.shared.lanes=0);Tn|=m,e.lanes=m,e.memoizedState=U}}function sc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var s=e[t],u=s.callback;if(u!==null){if(s.callback=null,s=n,typeof u!="function")throw Error(i(191,u));u.call(s)}}}var Xr={},Ot=nn(Xr),Zr=nn(Xr),ei=nn(Xr);function Pn(e){if(e===Xr)throw Error(i(174));return e}function tl(e,t){switch(pe(ei,t),pe(Zr,e),pe(Ot,Xr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:to(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=to(t,e)}ve(Ot),pe(Ot,t)}function ir(){ve(Ot),ve(Zr),ve(ei)}function oc(e){Pn(ei.current);var t=Pn(Ot.current),n=to(t,e.type);t!==n&&(pe(Zr,e),pe(Ot,n))}function nl(e){Zr.current===e&&(ve(Ot),ve(Zr))}var _e=nn(0);function us(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var rl=[];function il(){for(var e=0;e<rl.length;e++)rl[e]._workInProgressVersionPrimary=null;rl.length=0}var cs=W.ReactCurrentDispatcher,sl=W.ReactCurrentBatchConfig,jn=0,ke=null,Re=null,Ne=null,ds=!1,ti=!1,ni=0,up=0;function Fe(){throw Error(i(321))}function ol(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!vt(e[n],t[n]))return!1;return!0}function ll(e,t,n,s,u,d){if(jn=d,ke=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,cs.current=e===null||e.memoizedState===null?hp:pp,e=n(s,u),ti){d=0;do{if(ti=!1,ni=0,25<=d)throw Error(i(301));d+=1,Ne=Re=null,t.updateQueue=null,cs.current=mp,e=n(s,u)}while(ti)}if(cs.current=ps,t=Re!==null&&Re.next!==null,jn=0,Ne=Re=ke=null,ds=!1,t)throw Error(i(300));return e}function al(){var e=ni!==0;return ni=0,e}function Nt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ne===null?ke.memoizedState=Ne=e:Ne=Ne.next=e,Ne}function ft(){if(Re===null){var e=ke.alternate;e=e!==null?e.memoizedState:null}else e=Re.next;var t=Ne===null?ke.memoizedState:Ne.next;if(t!==null)Ne=t,Re=e;else{if(e===null)throw Error(i(310));Re=e,e={memoizedState:Re.memoizedState,baseState:Re.baseState,baseQueue:Re.baseQueue,queue:Re.queue,next:null},Ne===null?ke.memoizedState=Ne=e:Ne=Ne.next=e}return Ne}function ri(e,t){return typeof t=="function"?t(e):t}function ul(e){var t=ft(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var s=Re,u=s.baseQueue,d=n.pending;if(d!==null){if(u!==null){var m=u.next;u.next=d.next,d.next=m}s.baseQueue=u=d,n.pending=null}if(u!==null){d=u.next,s=s.baseState;var y=m=null,_=null,T=d;do{var b=T.lane;if((jn&b)===b)_!==null&&(_=_.next={lane:0,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null}),s=T.hasEagerState?T.eagerState:e(s,T.action);else{var U={lane:b,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null};_===null?(y=_=U,m=s):_=_.next=U,ke.lanes|=b,Tn|=b}T=T.next}while(T!==null&&T!==d);_===null?m=s:_.next=y,vt(s,t.memoizedState)||(Qe=!0),t.memoizedState=s,t.baseState=m,t.baseQueue=_,n.lastRenderedState=s}if(e=n.interleaved,e!==null){u=e;do d=u.lane,ke.lanes|=d,Tn|=d,u=u.next;while(u!==e)}else u===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function cl(e){var t=ft(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var s=n.dispatch,u=n.pending,d=t.memoizedState;if(u!==null){n.pending=null;var m=u=u.next;do d=e(d,m.action),m=m.next;while(m!==u);vt(d,t.memoizedState)||(Qe=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),n.lastRenderedState=d}return[d,s]}function lc(){}function ac(e,t){var n=ke,s=ft(),u=t(),d=!vt(s.memoizedState,u);if(d&&(s.memoizedState=u,Qe=!0),s=s.queue,dl(dc.bind(null,n,s,e),[e]),s.getSnapshot!==t||d||Ne!==null&&Ne.memoizedState.tag&1){if(n.flags|=2048,ii(9,cc.bind(null,n,s,u,t),void 0,null),Le===null)throw Error(i(349));(jn&30)!==0||uc(n,t,u)}return u}function uc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ke.updateQueue,t===null?(t={lastEffect:null,stores:null},ke.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function cc(e,t,n,s){t.value=n,t.getSnapshot=s,fc(t)&&hc(e)}function dc(e,t,n){return n(function(){fc(t)&&hc(e)})}function fc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!vt(e,n)}catch{return!0}}function hc(e){var t=Mt(e,1);t!==null&&St(t,e,1,-1)}function pc(e){var t=Nt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ri,lastRenderedState:e},t.queue=e,e=e.dispatch=fp.bind(null,ke,e),[t.memoizedState,e]}function ii(e,t,n,s){return e={tag:e,create:t,destroy:n,deps:s,next:null},t=ke.updateQueue,t===null?(t={lastEffect:null,stores:null},ke.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(s=n.next,n.next=e,e.next=s,t.lastEffect=e)),e}function mc(){return ft().memoizedState}function fs(e,t,n,s){var u=Nt();ke.flags|=e,u.memoizedState=ii(1|t,n,void 0,s===void 0?null:s)}function hs(e,t,n,s){var u=ft();s=s===void 0?null:s;var d=void 0;if(Re!==null){var m=Re.memoizedState;if(d=m.destroy,s!==null&&ol(s,m.deps)){u.memoizedState=ii(t,n,d,s);return}}ke.flags|=e,u.memoizedState=ii(1|t,n,d,s)}function gc(e,t){return fs(8390656,8,e,t)}function dl(e,t){return hs(2048,8,e,t)}function vc(e,t){return hs(4,2,e,t)}function yc(e,t){return hs(4,4,e,t)}function wc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function _c(e,t,n){return n=n!=null?n.concat([e]):null,hs(4,4,wc.bind(null,t,e),n)}function fl(){}function kc(e,t){var n=ft();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&ol(t,s[1])?s[0]:(n.memoizedState=[e,t],e)}function Sc(e,t){var n=ft();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&ol(t,s[1])?s[0]:(e=e(),n.memoizedState=[e,t],e)}function Ec(e,t,n){return(jn&21)===0?(e.baseState&&(e.baseState=!1,Qe=!0),e.memoizedState=n):(vt(n,t)||(n=Za(),ke.lanes|=n,Tn|=n,e.baseState=!0),t)}function cp(e,t){var n=fe;fe=n!==0&&4>n?n:4,e(!0);var s=sl.transition;sl.transition={};try{e(!1),t()}finally{fe=n,sl.transition=s}}function xc(){return ft().memoizedState}function dp(e,t,n){var s=dn(e);if(n={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null},Cc(e))Pc(t,n);else if(n=nc(e,t,n,s),n!==null){var u=Ve();St(n,e,s,u),jc(n,t,s)}}function fp(e,t,n){var s=dn(e),u={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null};if(Cc(e))Pc(t,u);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var m=t.lastRenderedState,y=d(m,n);if(u.hasEagerState=!0,u.eagerState=y,vt(y,m)){var _=t.interleaved;_===null?(u.next=u,Zo(t)):(u.next=_.next,_.next=u),t.interleaved=u;return}}catch{}finally{}n=nc(e,t,u,s),n!==null&&(u=Ve(),St(n,e,s,u),jc(n,t,s))}}function Cc(e){var t=e.alternate;return e===ke||t!==null&&t===ke}function Pc(e,t){ti=ds=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function jc(e,t,n){if((n&4194240)!==0){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,po(e,n)}}var ps={readContext:dt,useCallback:Fe,useContext:Fe,useEffect:Fe,useImperativeHandle:Fe,useInsertionEffect:Fe,useLayoutEffect:Fe,useMemo:Fe,useReducer:Fe,useRef:Fe,useState:Fe,useDebugValue:Fe,useDeferredValue:Fe,useTransition:Fe,useMutableSource:Fe,useSyncExternalStore:Fe,useId:Fe,unstable_isNewReconciler:!1},hp={readContext:dt,useCallback:function(e,t){return Nt().memoizedState=[e,t===void 0?null:t],e},useContext:dt,useEffect:gc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,fs(4194308,4,wc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return fs(4194308,4,e,t)},useInsertionEffect:function(e,t){return fs(4,2,e,t)},useMemo:function(e,t){var n=Nt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var s=Nt();return t=n!==void 0?n(t):t,s.memoizedState=s.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},s.queue=e,e=e.dispatch=dp.bind(null,ke,e),[s.memoizedState,e]},useRef:function(e){var t=Nt();return e={current:e},t.memoizedState=e},useState:pc,useDebugValue:fl,useDeferredValue:function(e){return Nt().memoizedState=e},useTransition:function(){var e=pc(!1),t=e[0];return e=cp.bind(null,e[1]),Nt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var s=ke,u=Nt();if(we){if(n===void 0)throw Error(i(407));n=n()}else{if(n=t(),Le===null)throw Error(i(349));(jn&30)!==0||uc(s,t,n)}u.memoizedState=n;var d={value:n,getSnapshot:t};return u.queue=d,gc(dc.bind(null,s,d,e),[e]),s.flags|=2048,ii(9,cc.bind(null,s,d,n,t),void 0,null),n},useId:function(){var e=Nt(),t=Le.identifierPrefix;if(we){var n=zt,s=Ut;n=(s&~(1<<32-gt(s)-1)).toString(32)+n,t=":"+t+"R"+n,n=ni++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=up++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},pp={readContext:dt,useCallback:kc,useContext:dt,useEffect:dl,useImperativeHandle:_c,useInsertionEffect:vc,useLayoutEffect:yc,useMemo:Sc,useReducer:ul,useRef:mc,useState:function(){return ul(ri)},useDebugValue:fl,useDeferredValue:function(e){var t=ft();return Ec(t,Re.memoizedState,e)},useTransition:function(){var e=ul(ri)[0],t=ft().memoizedState;return[e,t]},useMutableSource:lc,useSyncExternalStore:ac,useId:xc,unstable_isNewReconciler:!1},mp={readContext:dt,useCallback:kc,useContext:dt,useEffect:dl,useImperativeHandle:_c,useInsertionEffect:vc,useLayoutEffect:yc,useMemo:Sc,useReducer:cl,useRef:mc,useState:function(){return cl(ri)},useDebugValue:fl,useDeferredValue:function(e){var t=ft();return Re===null?t.memoizedState=e:Ec(t,Re.memoizedState,e)},useTransition:function(){var e=cl(ri)[0],t=ft().memoizedState;return[e,t]},useMutableSource:lc,useSyncExternalStore:ac,useId:xc,unstable_isNewReconciler:!1};function wt(e,t){if(e&&e.defaultProps){t=H({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function hl(e,t,n,s){t=e.memoizedState,n=n(s,t),n=n==null?t:H({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ms={isMounted:function(e){return(e=e._reactInternals)?_n(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var s=Ve(),u=dn(e),d=Ft(s,u);d.payload=t,n!=null&&(d.callback=n),t=ln(e,d,u),t!==null&&(St(t,e,u,s),ls(t,e,u))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var s=Ve(),u=dn(e),d=Ft(s,u);d.tag=1,d.payload=t,n!=null&&(d.callback=n),t=ln(e,d,u),t!==null&&(St(t,e,u,s),ls(t,e,u))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ve(),s=dn(e),u=Ft(n,s);u.tag=2,t!=null&&(u.callback=t),t=ln(e,u,s),t!==null&&(St(t,e,s,n),ls(t,e,s))}};function Tc(e,t,n,s,u,d,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,d,m):t.prototype&&t.prototype.isPureReactComponent?!Hr(n,s)||!Hr(u,d):!0}function Rc(e,t,n){var s=!1,u=rn,d=t.contextType;return typeof d=="object"&&d!==null?d=dt(d):(u=Ge(t)?Sn:Me.current,s=t.contextTypes,d=(s=s!=null)?Yn(e,u):rn),t=new t(n,d),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ms,e.stateNode=t,t._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=u,e.__reactInternalMemoizedMaskedChildContext=d),t}function Oc(e,t,n,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,s),t.state!==e&&ms.enqueueReplaceState(t,t.state,null)}function pl(e,t,n,s){var u=e.stateNode;u.props=n,u.state=e.memoizedState,u.refs={},el(e);var d=t.contextType;typeof d=="object"&&d!==null?u.context=dt(d):(d=Ge(t)?Sn:Me.current,u.context=Yn(e,d)),u.state=e.memoizedState,d=t.getDerivedStateFromProps,typeof d=="function"&&(hl(e,t,d,n),u.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(t=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),t!==u.state&&ms.enqueueReplaceState(u,u.state,null),as(e,n,u,s),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308)}function sr(e,t){try{var n="",s=t;do n+=ae(s),s=s.return;while(s);var u=n}catch(d){u=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:t,stack:u,digest:null}}function ml(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function gl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var gp=typeof WeakMap=="function"?WeakMap:Map;function Nc(e,t,n){n=Ft(-1,n),n.tag=3,n.payload={element:null};var s=t.value;return n.callback=function(){Ss||(Ss=!0,Nl=s),gl(e,t)},n}function Lc(e,t,n){n=Ft(-1,n),n.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var u=t.value;n.payload=function(){return s(u)},n.callback=function(){gl(e,t)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(n.callback=function(){gl(e,t),typeof s!="function"&&(un===null?un=new Set([this]):un.add(this));var m=t.stack;this.componentDidCatch(t.value,{componentStack:m!==null?m:""})}),n}function $c(e,t,n){var s=e.pingCache;if(s===null){s=e.pingCache=new gp;var u=new Set;s.set(t,u)}else u=s.get(t),u===void 0&&(u=new Set,s.set(t,u));u.has(n)||(u.add(n),e=Op.bind(null,e,t,n),t.then(e,e))}function Ic(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ac(e,t,n,s,u){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ft(-1,1),t.tag=2,ln(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=u,e)}var vp=W.ReactCurrentOwner,Qe=!1;function qe(e,t,n,s){t.child=e===null?tc(t,null,n,s):tr(t,e.child,n,s)}function bc(e,t,n,s,u){n=n.render;var d=t.ref;return rr(t,u),s=ll(e,t,n,s,d,u),n=al(),e!==null&&!Qe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,Bt(e,t,u)):(we&&n&&Ho(t),t.flags|=1,qe(e,t,s,u),t.child)}function Dc(e,t,n,s,u){if(e===null){var d=n.type;return typeof d=="function"&&!Ul(d)&&d.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=d,Uc(e,t,d,s,u)):(e=Ts(n.type,null,s,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,(e.lanes&u)===0){var m=d.memoizedProps;if(n=n.compare,n=n!==null?n:Hr,n(m,s)&&e.ref===t.ref)return Bt(e,t,u)}return t.flags|=1,e=hn(d,s),e.ref=t.ref,e.return=t,t.child=e}function Uc(e,t,n,s,u){if(e!==null){var d=e.memoizedProps;if(Hr(d,s)&&e.ref===t.ref)if(Qe=!1,t.pendingProps=s=d,(e.lanes&u)!==0)(e.flags&131072)!==0&&(Qe=!0);else return t.lanes=e.lanes,Bt(e,t,u)}return vl(e,t,n,s,u)}function zc(e,t,n){var s=t.pendingProps,u=s.children,d=e!==null?e.memoizedState:null;if(s.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},pe(lr,lt),lt|=n;else{if((n&1073741824)===0)return e=d!==null?d.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,pe(lr,lt),lt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=d!==null?d.baseLanes:n,pe(lr,lt),lt|=s}else d!==null?(s=d.baseLanes|n,t.memoizedState=null):s=n,pe(lr,lt),lt|=s;return qe(e,t,u,n),t.child}function Mc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function vl(e,t,n,s,u){var d=Ge(n)?Sn:Me.current;return d=Yn(t,d),rr(t,u),n=ll(e,t,n,s,d,u),s=al(),e!==null&&!Qe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,Bt(e,t,u)):(we&&s&&Ho(t),t.flags|=1,qe(e,t,n,u),t.child)}function Fc(e,t,n,s,u){if(Ge(n)){var d=!0;Zi(t)}else d=!1;if(rr(t,u),t.stateNode===null)vs(e,t),Rc(t,n,s),pl(t,n,s,u),s=!0;else if(e===null){var m=t.stateNode,y=t.memoizedProps;m.props=y;var _=m.context,T=n.contextType;typeof T=="object"&&T!==null?T=dt(T):(T=Ge(n)?Sn:Me.current,T=Yn(t,T));var b=n.getDerivedStateFromProps,U=typeof b=="function"||typeof m.getSnapshotBeforeUpdate=="function";U||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(y!==s||_!==T)&&Oc(t,m,s,T),on=!1;var I=t.memoizedState;m.state=I,as(t,s,m,u),_=t.memoizedState,y!==s||I!==_||Je.current||on?(typeof b=="function"&&(hl(t,n,b,s),_=t.memoizedState),(y=on||Tc(t,n,y,s,I,_,T))?(U||typeof m.UNSAFE_componentWillMount!="function"&&typeof m.componentWillMount!="function"||(typeof m.componentWillMount=="function"&&m.componentWillMount(),typeof m.UNSAFE_componentWillMount=="function"&&m.UNSAFE_componentWillMount()),typeof m.componentDidMount=="function"&&(t.flags|=4194308)):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=_),m.props=s,m.state=_,m.context=T,s=y):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{m=t.stateNode,rc(e,t),y=t.memoizedProps,T=t.type===t.elementType?y:wt(t.type,y),m.props=T,U=t.pendingProps,I=m.context,_=n.contextType,typeof _=="object"&&_!==null?_=dt(_):(_=Ge(n)?Sn:Me.current,_=Yn(t,_));var F=n.getDerivedStateFromProps;(b=typeof F=="function"||typeof m.getSnapshotBeforeUpdate=="function")||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(y!==U||I!==_)&&Oc(t,m,s,_),on=!1,I=t.memoizedState,m.state=I,as(t,s,m,u);var q=t.memoizedState;y!==U||I!==q||Je.current||on?(typeof F=="function"&&(hl(t,n,F,s),q=t.memoizedState),(T=on||Tc(t,n,T,s,I,q,_)||!1)?(b||typeof m.UNSAFE_componentWillUpdate!="function"&&typeof m.componentWillUpdate!="function"||(typeof m.componentWillUpdate=="function"&&m.componentWillUpdate(s,q,_),typeof m.UNSAFE_componentWillUpdate=="function"&&m.UNSAFE_componentWillUpdate(s,q,_)),typeof m.componentDidUpdate=="function"&&(t.flags|=4),typeof m.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof m.componentDidUpdate!="function"||y===e.memoizedProps&&I===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&I===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=q),m.props=s,m.state=q,m.context=_,s=T):(typeof m.componentDidUpdate!="function"||y===e.memoizedProps&&I===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&I===e.memoizedState||(t.flags|=1024),s=!1)}return yl(e,t,n,s,d,u)}function yl(e,t,n,s,u,d){Mc(e,t);var m=(t.flags&128)!==0;if(!s&&!m)return u&&Vu(t,n,!1),Bt(e,t,d);s=t.stateNode,vp.current=t;var y=m&&typeof n.getDerivedStateFromError!="function"?null:s.render();return t.flags|=1,e!==null&&m?(t.child=tr(t,e.child,null,d),t.child=tr(t,null,y,d)):qe(e,t,y,d),t.memoizedState=s.state,u&&Vu(t,n,!0),t.child}function Bc(e){var t=e.stateNode;t.pendingContext?Hu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Hu(e,t.context,!1),tl(e,t.containerInfo)}function Wc(e,t,n,s,u){return er(),Jo(u),t.flags|=256,qe(e,t,n,s),t.child}var wl={dehydrated:null,treeContext:null,retryLane:0};function _l(e){return{baseLanes:e,cachePool:null,transitions:null}}function Hc(e,t,n){var s=t.pendingProps,u=_e.current,d=!1,m=(t.flags&128)!==0,y;if((y=m)||(y=e!==null&&e.memoizedState===null?!1:(u&2)!==0),y?(d=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(u|=1),pe(_e,u&1),e===null)return Ko(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(m=s.children,e=s.fallback,d?(s=t.mode,d=t.child,m={mode:"hidden",children:m},(s&1)===0&&d!==null?(d.childLanes=0,d.pendingProps=m):d=Rs(m,s,0,null),e=Ln(e,s,n,null),d.return=t,e.return=t,d.sibling=e,t.child=d,t.child.memoizedState=_l(n),t.memoizedState=wl,e):kl(t,m));if(u=e.memoizedState,u!==null&&(y=u.dehydrated,y!==null))return yp(e,t,m,s,y,u,n);if(d){d=s.fallback,m=t.mode,u=e.child,y=u.sibling;var _={mode:"hidden",children:s.children};return(m&1)===0&&t.child!==u?(s=t.child,s.childLanes=0,s.pendingProps=_,t.deletions=null):(s=hn(u,_),s.subtreeFlags=u.subtreeFlags&14680064),y!==null?d=hn(y,d):(d=Ln(d,m,n,null),d.flags|=2),d.return=t,s.return=t,s.sibling=d,t.child=s,s=d,d=t.child,m=e.child.memoizedState,m=m===null?_l(n):{baseLanes:m.baseLanes|n,cachePool:null,transitions:m.transitions},d.memoizedState=m,d.childLanes=e.childLanes&~n,t.memoizedState=wl,s}return d=e.child,e=d.sibling,s=hn(d,{mode:"visible",children:s.children}),(t.mode&1)===0&&(s.lanes=n),s.return=t,s.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=s,t.memoizedState=null,s}function kl(e,t){return t=Rs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function gs(e,t,n,s){return s!==null&&Jo(s),tr(t,e.child,null,n),e=kl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function yp(e,t,n,s,u,d,m){if(n)return t.flags&256?(t.flags&=-257,s=ml(Error(i(422))),gs(e,t,m,s)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(d=s.fallback,u=t.mode,s=Rs({mode:"visible",children:s.children},u,0,null),d=Ln(d,u,m,null),d.flags|=2,s.return=t,d.return=t,s.sibling=d,t.child=s,(t.mode&1)!==0&&tr(t,e.child,null,m),t.child.memoizedState=_l(m),t.memoizedState=wl,d);if((t.mode&1)===0)return gs(e,t,m,null);if(u.data==="$!"){if(s=u.nextSibling&&u.nextSibling.dataset,s)var y=s.dgst;return s=y,d=Error(i(419)),s=ml(d,s,void 0),gs(e,t,m,s)}if(y=(m&e.childLanes)!==0,Qe||y){if(s=Le,s!==null){switch(m&-m){case 4:u=2;break;case 16:u=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:u=32;break;case 536870912:u=268435456;break;default:u=0}u=(u&(s.suspendedLanes|m))!==0?0:u,u!==0&&u!==d.retryLane&&(d.retryLane=u,Mt(e,u),St(s,e,u,-1))}return Dl(),s=ml(Error(i(421))),gs(e,t,m,s)}return u.data==="$?"?(t.flags|=128,t.child=e.child,t=Np.bind(null,e),u._reactRetry=t,null):(e=d.treeContext,ot=tn(u.nextSibling),st=t,we=!0,yt=null,e!==null&&(ut[ct++]=Ut,ut[ct++]=zt,ut[ct++]=En,Ut=e.id,zt=e.overflow,En=t),t=kl(t,s.children),t.flags|=4096,t)}function qc(e,t,n){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),Xo(e.return,t,n)}function Sl(e,t,n,s,u){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:n,tailMode:u}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=s,d.tail=n,d.tailMode=u)}function Vc(e,t,n){var s=t.pendingProps,u=s.revealOrder,d=s.tail;if(qe(e,t,s.children,n),s=_e.current,(s&2)!==0)s=s&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&qc(e,n,t);else if(e.tag===19)qc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(pe(_e,s),(t.mode&1)===0)t.memoizedState=null;else switch(u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&us(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),Sl(t,!1,u,n,d);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&us(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}Sl(t,!0,n,null,d);break;case"together":Sl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function vs(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Bt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Tn|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,n=hn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=hn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function wp(e,t,n){switch(t.tag){case 3:Bc(t),er();break;case 5:oc(t);break;case 1:Ge(t.type)&&Zi(t);break;case 4:tl(t,t.stateNode.containerInfo);break;case 10:var s=t.type._context,u=t.memoizedProps.value;pe(ss,s._currentValue),s._currentValue=u;break;case 13:if(s=t.memoizedState,s!==null)return s.dehydrated!==null?(pe(_e,_e.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?Hc(e,t,n):(pe(_e,_e.current&1),e=Bt(e,t,n),e!==null?e.sibling:null);pe(_e,_e.current&1);break;case 19:if(s=(n&t.childLanes)!==0,(e.flags&128)!==0){if(s)return Vc(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),pe(_e,_e.current),s)break;return null;case 22:case 23:return t.lanes=0,zc(e,t,n)}return Bt(e,t,n)}var Kc,El,Jc,Gc;Kc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},El=function(){},Jc=function(e,t,n,s){var u=e.memoizedProps;if(u!==s){e=t.stateNode,Pn(Ot.current);var d=null;switch(n){case"input":u=Ys(e,u),s=Ys(e,s),d=[];break;case"select":u=H({},u,{value:void 0}),s=H({},s,{value:void 0}),d=[];break;case"textarea":u=eo(e,u),s=eo(e,s),d=[];break;default:typeof u.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=Qi)}no(n,s);var m;n=null;for(T in u)if(!s.hasOwnProperty(T)&&u.hasOwnProperty(T)&&u[T]!=null)if(T==="style"){var y=u[T];for(m in y)y.hasOwnProperty(m)&&(n||(n={}),n[m]="")}else T!=="dangerouslySetInnerHTML"&&T!=="children"&&T!=="suppressContentEditableWarning"&&T!=="suppressHydrationWarning"&&T!=="autoFocus"&&(a.hasOwnProperty(T)?d||(d=[]):(d=d||[]).push(T,null));for(T in s){var _=s[T];if(y=u?.[T],s.hasOwnProperty(T)&&_!==y&&(_!=null||y!=null))if(T==="style")if(y){for(m in y)!y.hasOwnProperty(m)||_&&_.hasOwnProperty(m)||(n||(n={}),n[m]="");for(m in _)_.hasOwnProperty(m)&&y[m]!==_[m]&&(n||(n={}),n[m]=_[m])}else n||(d||(d=[]),d.push(T,n)),n=_;else T==="dangerouslySetInnerHTML"?(_=_?_.__html:void 0,y=y?y.__html:void 0,_!=null&&y!==_&&(d=d||[]).push(T,_)):T==="children"?typeof _!="string"&&typeof _!="number"||(d=d||[]).push(T,""+_):T!=="suppressContentEditableWarning"&&T!=="suppressHydrationWarning"&&(a.hasOwnProperty(T)?(_!=null&&T==="onScroll"&&ge("scroll",e),d||y===_||(d=[])):(d=d||[]).push(T,_))}n&&(d=d||[]).push("style",n);var T=d;(t.updateQueue=T)&&(t.flags|=4)}},Gc=function(e,t,n,s){n!==s&&(t.flags|=4)};function si(e,t){if(!we)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function Be(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,s=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,s|=u.subtreeFlags&14680064,s|=u.flags&14680064,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,s|=u.subtreeFlags,s|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=s,e.childLanes=n,t}function _p(e,t,n){var s=t.pendingProps;switch(qo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Be(t),null;case 1:return Ge(t.type)&&Xi(),Be(t),null;case 3:return s=t.stateNode,ir(),ve(Je),ve(Me),il(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(rs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,yt!==null&&(Il(yt),yt=null))),El(e,t),Be(t),null;case 5:nl(t);var u=Pn(ei.current);if(n=t.type,e!==null&&t.stateNode!=null)Jc(e,t,n,s,u),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!s){if(t.stateNode===null)throw Error(i(166));return Be(t),null}if(e=Pn(Ot.current),rs(t)){s=t.stateNode,n=t.type;var d=t.memoizedProps;switch(s[Rt]=t,s[Gr]=d,e=(t.mode&1)!==0,n){case"dialog":ge("cancel",s),ge("close",s);break;case"iframe":case"object":case"embed":ge("load",s);break;case"video":case"audio":for(u=0;u<Vr.length;u++)ge(Vr[u],s);break;case"source":ge("error",s);break;case"img":case"image":case"link":ge("error",s),ge("load",s);break;case"details":ge("toggle",s);break;case"input":Ra(s,d),ge("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!d.multiple},ge("invalid",s);break;case"textarea":La(s,d),ge("invalid",s)}no(n,d),u=null;for(var m in d)if(d.hasOwnProperty(m)){var y=d[m];m==="children"?typeof y=="string"?s.textContent!==y&&(d.suppressHydrationWarning!==!0&&Gi(s.textContent,y,e),u=["children",y]):typeof y=="number"&&s.textContent!==""+y&&(d.suppressHydrationWarning!==!0&&Gi(s.textContent,y,e),u=["children",""+y]):a.hasOwnProperty(m)&&y!=null&&m==="onScroll"&&ge("scroll",s)}switch(n){case"input":Pi(s),Na(s,d,!0);break;case"textarea":Pi(s),Ia(s);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(s.onclick=Qi)}s=u,t.updateQueue=s,s!==null&&(t.flags|=4)}else{m=u.nodeType===9?u:u.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Aa(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=m.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=m.createElement(n,{is:s.is}):(e=m.createElement(n),n==="select"&&(m=e,s.multiple?m.multiple=!0:s.size&&(m.size=s.size))):e=m.createElementNS(e,n),e[Rt]=t,e[Gr]=s,Kc(e,t,!1,!1),t.stateNode=e;e:{switch(m=ro(n,s),n){case"dialog":ge("cancel",e),ge("close",e),u=s;break;case"iframe":case"object":case"embed":ge("load",e),u=s;break;case"video":case"audio":for(u=0;u<Vr.length;u++)ge(Vr[u],e);u=s;break;case"source":ge("error",e),u=s;break;case"img":case"image":case"link":ge("error",e),ge("load",e),u=s;break;case"details":ge("toggle",e),u=s;break;case"input":Ra(e,s),u=Ys(e,s),ge("invalid",e);break;case"option":u=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},u=H({},s,{value:void 0}),ge("invalid",e);break;case"textarea":La(e,s),u=eo(e,s),ge("invalid",e);break;default:u=s}no(n,u),y=u;for(d in y)if(y.hasOwnProperty(d)){var _=y[d];d==="style"?Ua(e,_):d==="dangerouslySetInnerHTML"?(_=_?_.__html:void 0,_!=null&&ba(e,_)):d==="children"?typeof _=="string"?(n!=="textarea"||_!=="")&&jr(e,_):typeof _=="number"&&jr(e,""+_):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(a.hasOwnProperty(d)?_!=null&&d==="onScroll"&&ge("scroll",e):_!=null&&D(e,d,_,m))}switch(n){case"input":Pi(e),Na(e,s,!1);break;case"textarea":Pi(e),Ia(e);break;case"option":s.value!=null&&e.setAttribute("value",""+de(s.value));break;case"select":e.multiple=!!s.multiple,d=s.value,d!=null?zn(e,!!s.multiple,d,!1):s.defaultValue!=null&&zn(e,!!s.multiple,s.defaultValue,!0);break;default:typeof u.onClick=="function"&&(e.onclick=Qi)}switch(n){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Be(t),null;case 6:if(e&&t.stateNode!=null)Gc(e,t,e.memoizedProps,s);else{if(typeof s!="string"&&t.stateNode===null)throw Error(i(166));if(n=Pn(ei.current),Pn(Ot.current),rs(t)){if(s=t.stateNode,n=t.memoizedProps,s[Rt]=t,(d=s.nodeValue!==n)&&(e=st,e!==null))switch(e.tag){case 3:Gi(s.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Gi(s.nodeValue,n,(e.mode&1)!==0)}d&&(t.flags|=4)}else s=(n.nodeType===9?n:n.ownerDocument).createTextNode(s),s[Rt]=t,t.stateNode=s}return Be(t),null;case 13:if(ve(_e),s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(we&&ot!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Xu(),er(),t.flags|=98560,d=!1;else if(d=rs(t),s!==null&&s.dehydrated!==null){if(e===null){if(!d)throw Error(i(318));if(d=t.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(i(317));d[Rt]=t}else er(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Be(t),d=!1}else yt!==null&&(Il(yt),yt=null),d=!0;if(!d)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(_e.current&1)!==0?Oe===0&&(Oe=3):Dl())),t.updateQueue!==null&&(t.flags|=4),Be(t),null);case 4:return ir(),El(e,t),e===null&&Kr(t.stateNode.containerInfo),Be(t),null;case 10:return Yo(t.type._context),Be(t),null;case 17:return Ge(t.type)&&Xi(),Be(t),null;case 19:if(ve(_e),d=t.memoizedState,d===null)return Be(t),null;if(s=(t.flags&128)!==0,m=d.rendering,m===null)if(s)si(d,!1);else{if(Oe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(m=us(e),m!==null){for(t.flags|=128,si(d,!1),s=m.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),t.subtreeFlags=0,s=n,n=t.child;n!==null;)d=n,e=s,d.flags&=14680066,m=d.alternate,m===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=m.childLanes,d.lanes=m.lanes,d.child=m.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=m.memoizedProps,d.memoizedState=m.memoizedState,d.updateQueue=m.updateQueue,d.type=m.type,e=m.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return pe(_e,_e.current&1|2),t.child}e=e.sibling}d.tail!==null&&Ce()>ar&&(t.flags|=128,s=!0,si(d,!1),t.lanes=4194304)}else{if(!s)if(e=us(m),e!==null){if(t.flags|=128,s=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),si(d,!0),d.tail===null&&d.tailMode==="hidden"&&!m.alternate&&!we)return Be(t),null}else 2*Ce()-d.renderingStartTime>ar&&n!==1073741824&&(t.flags|=128,s=!0,si(d,!1),t.lanes=4194304);d.isBackwards?(m.sibling=t.child,t.child=m):(n=d.last,n!==null?n.sibling=m:t.child=m,d.last=m)}return d.tail!==null?(t=d.tail,d.rendering=t,d.tail=t.sibling,d.renderingStartTime=Ce(),t.sibling=null,n=_e.current,pe(_e,s?n&1|2:n&1),t):(Be(t),null);case 22:case 23:return bl(),s=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(t.flags|=8192),s&&(t.mode&1)!==0?(lt&1073741824)!==0&&(Be(t),t.subtreeFlags&6&&(t.flags|=8192)):Be(t),null;case 24:return null;case 25:return null}throw Error(i(156,t.tag))}function kp(e,t){switch(qo(t),t.tag){case 1:return Ge(t.type)&&Xi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ir(),ve(Je),ve(Me),il(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return nl(t),null;case 13:if(ve(_e),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));er()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ve(_e),null;case 4:return ir(),null;case 10:return Yo(t.type._context),null;case 22:case 23:return bl(),null;case 24:return null;default:return null}}var ys=!1,We=!1,Sp=typeof WeakSet=="function"?WeakSet:Set,B=null;function or(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(s){xe(e,t,s)}else n.current=null}function xl(e,t,n){try{n()}catch(s){xe(e,t,s)}}var Qc=!1;function Ep(e,t){if(bo=Ui,e=Tu(),To(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var s=n.getSelection&&n.getSelection();if(s&&s.rangeCount!==0){n=s.anchorNode;var u=s.anchorOffset,d=s.focusNode;s=s.focusOffset;try{n.nodeType,d.nodeType}catch{n=null;break e}var m=0,y=-1,_=-1,T=0,b=0,U=e,I=null;t:for(;;){for(var F;U!==n||u!==0&&U.nodeType!==3||(y=m+u),U!==d||s!==0&&U.nodeType!==3||(_=m+s),U.nodeType===3&&(m+=U.nodeValue.length),(F=U.firstChild)!==null;)I=U,U=F;for(;;){if(U===e)break t;if(I===n&&++T===u&&(y=m),I===d&&++b===s&&(_=m),(F=U.nextSibling)!==null)break;U=I,I=U.parentNode}U=F}n=y===-1||_===-1?null:{start:y,end:_}}else n=null}n=n||{start:0,end:0}}else n=null;for(Do={focusedElem:e,selectionRange:n},Ui=!1,B=t;B!==null;)if(t=B,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,B=e;else for(;B!==null;){t=B;try{var q=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(q!==null){var V=q.memoizedProps,Pe=q.memoizedState,C=t.stateNode,k=C.getSnapshotBeforeUpdate(t.elementType===t.type?V:wt(t.type,V),Pe);C.__reactInternalSnapshotBeforeUpdate=k}break;case 3:var P=t.stateNode.containerInfo;P.nodeType===1?P.textContent="":P.nodeType===9&&P.documentElement&&P.removeChild(P.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(i(163))}}catch(z){xe(t,t.return,z)}if(e=t.sibling,e!==null){e.return=t.return,B=e;break}B=t.return}return q=Qc,Qc=!1,q}function oi(e,t,n){var s=t.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var u=s=s.next;do{if((u.tag&e)===e){var d=u.destroy;u.destroy=void 0,d!==void 0&&xl(t,n,d)}u=u.next}while(u!==s)}}function ws(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var s=n.create;n.destroy=s()}n=n.next}while(n!==t)}}function Cl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Yc(e){var t=e.alternate;t!==null&&(e.alternate=null,Yc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Rt],delete t[Gr],delete t[Fo],delete t[sp],delete t[op])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Xc(e){return e.tag===5||e.tag===3||e.tag===4}function Zc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Xc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Pl(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Qi));else if(s!==4&&(e=e.child,e!==null))for(Pl(e,t,n),e=e.sibling;e!==null;)Pl(e,t,n),e=e.sibling}function jl(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(jl(e,t,n),e=e.sibling;e!==null;)jl(e,t,n),e=e.sibling}var De=null,_t=!1;function an(e,t,n){for(n=n.child;n!==null;)ed(e,t,n),n=n.sibling}function ed(e,t,n){if(Tt&&typeof Tt.onCommitFiberUnmount=="function")try{Tt.onCommitFiberUnmount(Li,n)}catch{}switch(n.tag){case 5:We||or(n,t);case 6:var s=De,u=_t;De=null,an(e,t,n),De=s,_t=u,De!==null&&(_t?(e=De,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):De.removeChild(n.stateNode));break;case 18:De!==null&&(_t?(e=De,n=n.stateNode,e.nodeType===8?Mo(e.parentNode,n):e.nodeType===1&&Mo(e,n),Ur(e)):Mo(De,n.stateNode));break;case 4:s=De,u=_t,De=n.stateNode.containerInfo,_t=!0,an(e,t,n),De=s,_t=u;break;case 0:case 11:case 14:case 15:if(!We&&(s=n.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){u=s=s.next;do{var d=u,m=d.destroy;d=d.tag,m!==void 0&&((d&2)!==0||(d&4)!==0)&&xl(n,t,m),u=u.next}while(u!==s)}an(e,t,n);break;case 1:if(!We&&(or(n,t),s=n.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=n.memoizedProps,s.state=n.memoizedState,s.componentWillUnmount()}catch(y){xe(n,t,y)}an(e,t,n);break;case 21:an(e,t,n);break;case 22:n.mode&1?(We=(s=We)||n.memoizedState!==null,an(e,t,n),We=s):an(e,t,n);break;default:an(e,t,n)}}function td(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Sp),t.forEach(function(s){var u=Lp.bind(null,e,s);n.has(s)||(n.add(s),s.then(u,u))})}}function kt(e,t){var n=t.deletions;if(n!==null)for(var s=0;s<n.length;s++){var u=n[s];try{var d=e,m=t,y=m;e:for(;y!==null;){switch(y.tag){case 5:De=y.stateNode,_t=!1;break e;case 3:De=y.stateNode.containerInfo,_t=!0;break e;case 4:De=y.stateNode.containerInfo,_t=!0;break e}y=y.return}if(De===null)throw Error(i(160));ed(d,m,u),De=null,_t=!1;var _=u.alternate;_!==null&&(_.return=null),u.return=null}catch(T){xe(u,t,T)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)nd(t,e),t=t.sibling}function nd(e,t){var n=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(kt(t,e),Lt(e),s&4){try{oi(3,e,e.return),ws(3,e)}catch(V){xe(e,e.return,V)}try{oi(5,e,e.return)}catch(V){xe(e,e.return,V)}}break;case 1:kt(t,e),Lt(e),s&512&&n!==null&&or(n,n.return);break;case 5:if(kt(t,e),Lt(e),s&512&&n!==null&&or(n,n.return),e.flags&32){var u=e.stateNode;try{jr(u,"")}catch(V){xe(e,e.return,V)}}if(s&4&&(u=e.stateNode,u!=null)){var d=e.memoizedProps,m=n!==null?n.memoizedProps:d,y=e.type,_=e.updateQueue;if(e.updateQueue=null,_!==null)try{y==="input"&&d.type==="radio"&&d.name!=null&&Oa(u,d),ro(y,m);var T=ro(y,d);for(m=0;m<_.length;m+=2){var b=_[m],U=_[m+1];b==="style"?Ua(u,U):b==="dangerouslySetInnerHTML"?ba(u,U):b==="children"?jr(u,U):D(u,b,U,T)}switch(y){case"input":Xs(u,d);break;case"textarea":$a(u,d);break;case"select":var I=u._wrapperState.wasMultiple;u._wrapperState.wasMultiple=!!d.multiple;var F=d.value;F!=null?zn(u,!!d.multiple,F,!1):I!==!!d.multiple&&(d.defaultValue!=null?zn(u,!!d.multiple,d.defaultValue,!0):zn(u,!!d.multiple,d.multiple?[]:"",!1))}u[Gr]=d}catch(V){xe(e,e.return,V)}}break;case 6:if(kt(t,e),Lt(e),s&4){if(e.stateNode===null)throw Error(i(162));u=e.stateNode,d=e.memoizedProps;try{u.nodeValue=d}catch(V){xe(e,e.return,V)}}break;case 3:if(kt(t,e),Lt(e),s&4&&n!==null&&n.memoizedState.isDehydrated)try{Ur(t.containerInfo)}catch(V){xe(e,e.return,V)}break;case 4:kt(t,e),Lt(e);break;case 13:kt(t,e),Lt(e),u=e.child,u.flags&8192&&(d=u.memoizedState!==null,u.stateNode.isHidden=d,!d||u.alternate!==null&&u.alternate.memoizedState!==null||(Ol=Ce())),s&4&&td(e);break;case 22:if(b=n!==null&&n.memoizedState!==null,e.mode&1?(We=(T=We)||b,kt(t,e),We=T):kt(t,e),Lt(e),s&8192){if(T=e.memoizedState!==null,(e.stateNode.isHidden=T)&&!b&&(e.mode&1)!==0)for(B=e,b=e.child;b!==null;){for(U=B=b;B!==null;){switch(I=B,F=I.child,I.tag){case 0:case 11:case 14:case 15:oi(4,I,I.return);break;case 1:or(I,I.return);var q=I.stateNode;if(typeof q.componentWillUnmount=="function"){s=I,n=I.return;try{t=s,q.props=t.memoizedProps,q.state=t.memoizedState,q.componentWillUnmount()}catch(V){xe(s,n,V)}}break;case 5:or(I,I.return);break;case 22:if(I.memoizedState!==null){sd(U);continue}}F!==null?(F.return=I,B=F):sd(U)}b=b.sibling}e:for(b=null,U=e;;){if(U.tag===5){if(b===null){b=U;try{u=U.stateNode,T?(d=u.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(y=U.stateNode,_=U.memoizedProps.style,m=_!=null&&_.hasOwnProperty("display")?_.display:null,y.style.display=Da("display",m))}catch(V){xe(e,e.return,V)}}}else if(U.tag===6){if(b===null)try{U.stateNode.nodeValue=T?"":U.memoizedProps}catch(V){xe(e,e.return,V)}}else if((U.tag!==22&&U.tag!==23||U.memoizedState===null||U===e)&&U.child!==null){U.child.return=U,U=U.child;continue}if(U===e)break e;for(;U.sibling===null;){if(U.return===null||U.return===e)break e;b===U&&(b=null),U=U.return}b===U&&(b=null),U.sibling.return=U.return,U=U.sibling}}break;case 19:kt(t,e),Lt(e),s&4&&td(e);break;case 21:break;default:kt(t,e),Lt(e)}}function Lt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Xc(n)){var s=n;break e}n=n.return}throw Error(i(160))}switch(s.tag){case 5:var u=s.stateNode;s.flags&32&&(jr(u,""),s.flags&=-33);var d=Zc(e);jl(e,d,u);break;case 3:case 4:var m=s.stateNode.containerInfo,y=Zc(e);Pl(e,y,m);break;default:throw Error(i(161))}}catch(_){xe(e,e.return,_)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function xp(e,t,n){B=e,rd(e)}function rd(e,t,n){for(var s=(e.mode&1)!==0;B!==null;){var u=B,d=u.child;if(u.tag===22&&s){var m=u.memoizedState!==null||ys;if(!m){var y=u.alternate,_=y!==null&&y.memoizedState!==null||We;y=ys;var T=We;if(ys=m,(We=_)&&!T)for(B=u;B!==null;)m=B,_=m.child,m.tag===22&&m.memoizedState!==null?od(u):_!==null?(_.return=m,B=_):od(u);for(;d!==null;)B=d,rd(d),d=d.sibling;B=u,ys=y,We=T}id(e)}else(u.subtreeFlags&8772)!==0&&d!==null?(d.return=u,B=d):id(e)}}function id(e){for(;B!==null;){var t=B;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:We||ws(5,t);break;case 1:var s=t.stateNode;if(t.flags&4&&!We)if(n===null)s.componentDidMount();else{var u=t.elementType===t.type?n.memoizedProps:wt(t.type,n.memoizedProps);s.componentDidUpdate(u,n.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var d=t.updateQueue;d!==null&&sc(t,d,s);break;case 3:var m=t.updateQueue;if(m!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}sc(t,m,n)}break;case 5:var y=t.stateNode;if(n===null&&t.flags&4){n=y;var _=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":_.autoFocus&&n.focus();break;case"img":_.src&&(n.src=_.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var T=t.alternate;if(T!==null){var b=T.memoizedState;if(b!==null){var U=b.dehydrated;U!==null&&Ur(U)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(i(163))}We||t.flags&512&&Cl(t)}catch(I){xe(t,t.return,I)}}if(t===e){B=null;break}if(n=t.sibling,n!==null){n.return=t.return,B=n;break}B=t.return}}function sd(e){for(;B!==null;){var t=B;if(t===e){B=null;break}var n=t.sibling;if(n!==null){n.return=t.return,B=n;break}B=t.return}}function od(e){for(;B!==null;){var t=B;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ws(4,t)}catch(_){xe(t,n,_)}break;case 1:var s=t.stateNode;if(typeof s.componentDidMount=="function"){var u=t.return;try{s.componentDidMount()}catch(_){xe(t,u,_)}}var d=t.return;try{Cl(t)}catch(_){xe(t,d,_)}break;case 5:var m=t.return;try{Cl(t)}catch(_){xe(t,m,_)}}}catch(_){xe(t,t.return,_)}if(t===e){B=null;break}var y=t.sibling;if(y!==null){y.return=t.return,B=y;break}B=t.return}}var Cp=Math.ceil,_s=W.ReactCurrentDispatcher,Tl=W.ReactCurrentOwner,ht=W.ReactCurrentBatchConfig,le=0,Le=null,Te=null,Ue=0,lt=0,lr=nn(0),Oe=0,li=null,Tn=0,ks=0,Rl=0,ai=null,Ye=null,Ol=0,ar=1/0,Wt=null,Ss=!1,Nl=null,un=null,Es=!1,cn=null,xs=0,ui=0,Ll=null,Cs=-1,Ps=0;function Ve(){return(le&6)!==0?Ce():Cs!==-1?Cs:Cs=Ce()}function dn(e){return(e.mode&1)===0?1:(le&2)!==0&&Ue!==0?Ue&-Ue:ap.transition!==null?(Ps===0&&(Ps=Za()),Ps):(e=fe,e!==0||(e=window.event,e=e===void 0?16:au(e.type)),e)}function St(e,t,n,s){if(50<ui)throw ui=0,Ll=null,Error(i(185));$r(e,n,s),((le&2)===0||e!==Le)&&(e===Le&&((le&2)===0&&(ks|=n),Oe===4&&fn(e,Ue)),Xe(e,s),n===1&&le===0&&(t.mode&1)===0&&(ar=Ce()+500,es&&sn()))}function Xe(e,t){var n=e.callbackNode;ah(e,t);var s=Ai(e,e===Le?Ue:0);if(s===0)n!==null&&Qa(n),e.callbackNode=null,e.callbackPriority=0;else if(t=s&-s,e.callbackPriority!==t){if(n!=null&&Qa(n),t===1)e.tag===0?lp(ad.bind(null,e)):Ku(ad.bind(null,e)),rp(function(){(le&6)===0&&sn()}),n=null;else{switch(eu(s)){case 1:n=co;break;case 4:n=Ya;break;case 16:n=Ni;break;case 536870912:n=Xa;break;default:n=Ni}n=gd(n,ld.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ld(e,t){if(Cs=-1,Ps=0,(le&6)!==0)throw Error(i(327));var n=e.callbackNode;if(ur()&&e.callbackNode!==n)return null;var s=Ai(e,e===Le?Ue:0);if(s===0)return null;if((s&30)!==0||(s&e.expiredLanes)!==0||t)t=js(e,s);else{t=s;var u=le;le|=2;var d=cd();(Le!==e||Ue!==t)&&(Wt=null,ar=Ce()+500,On(e,t));do try{Tp();break}catch(y){ud(e,y)}while(!0);Qo(),_s.current=d,le=u,Te!==null?t=0:(Le=null,Ue=0,t=Oe)}if(t!==0){if(t===2&&(u=fo(e),u!==0&&(s=u,t=$l(e,u))),t===1)throw n=li,On(e,0),fn(e,s),Xe(e,Ce()),n;if(t===6)fn(e,s);else{if(u=e.current.alternate,(s&30)===0&&!Pp(u)&&(t=js(e,s),t===2&&(d=fo(e),d!==0&&(s=d,t=$l(e,d))),t===1))throw n=li,On(e,0),fn(e,s),Xe(e,Ce()),n;switch(e.finishedWork=u,e.finishedLanes=s,t){case 0:case 1:throw Error(i(345));case 2:Nn(e,Ye,Wt);break;case 3:if(fn(e,s),(s&130023424)===s&&(t=Ol+500-Ce(),10<t)){if(Ai(e,0)!==0)break;if(u=e.suspendedLanes,(u&s)!==s){Ve(),e.pingedLanes|=e.suspendedLanes&u;break}e.timeoutHandle=zo(Nn.bind(null,e,Ye,Wt),t);break}Nn(e,Ye,Wt);break;case 4:if(fn(e,s),(s&4194240)===s)break;for(t=e.eventTimes,u=-1;0<s;){var m=31-gt(s);d=1<<m,m=t[m],m>u&&(u=m),s&=~d}if(s=u,s=Ce()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*Cp(s/1960))-s,10<s){e.timeoutHandle=zo(Nn.bind(null,e,Ye,Wt),s);break}Nn(e,Ye,Wt);break;case 5:Nn(e,Ye,Wt);break;default:throw Error(i(329))}}}return Xe(e,Ce()),e.callbackNode===n?ld.bind(null,e):null}function $l(e,t){var n=ai;return e.current.memoizedState.isDehydrated&&(On(e,t).flags|=256),e=js(e,t),e!==2&&(t=Ye,Ye=n,t!==null&&Il(t)),e}function Il(e){Ye===null?Ye=e:Ye.push.apply(Ye,e)}function Pp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var s=0;s<n.length;s++){var u=n[s],d=u.getSnapshot;u=u.value;try{if(!vt(d(),u))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function fn(e,t){for(t&=~Rl,t&=~ks,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-gt(t),s=1<<n;e[n]=-1,t&=~s}}function ad(e){if((le&6)!==0)throw Error(i(327));ur();var t=Ai(e,0);if((t&1)===0)return Xe(e,Ce()),null;var n=js(e,t);if(e.tag!==0&&n===2){var s=fo(e);s!==0&&(t=s,n=$l(e,s))}if(n===1)throw n=li,On(e,0),fn(e,t),Xe(e,Ce()),n;if(n===6)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Nn(e,Ye,Wt),Xe(e,Ce()),null}function Al(e,t){var n=le;le|=1;try{return e(t)}finally{le=n,le===0&&(ar=Ce()+500,es&&sn())}}function Rn(e){cn!==null&&cn.tag===0&&(le&6)===0&&ur();var t=le;le|=1;var n=ht.transition,s=fe;try{if(ht.transition=null,fe=1,e)return e()}finally{fe=s,ht.transition=n,le=t,(le&6)===0&&sn()}}function bl(){lt=lr.current,ve(lr)}function On(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,np(n)),Te!==null)for(n=Te.return;n!==null;){var s=n;switch(qo(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&Xi();break;case 3:ir(),ve(Je),ve(Me),il();break;case 5:nl(s);break;case 4:ir();break;case 13:ve(_e);break;case 19:ve(_e);break;case 10:Yo(s.type._context);break;case 22:case 23:bl()}n=n.return}if(Le=e,Te=e=hn(e.current,null),Ue=lt=t,Oe=0,li=null,Rl=ks=Tn=0,Ye=ai=null,Cn!==null){for(t=0;t<Cn.length;t++)if(n=Cn[t],s=n.interleaved,s!==null){n.interleaved=null;var u=s.next,d=n.pending;if(d!==null){var m=d.next;d.next=u,s.next=m}n.pending=s}Cn=null}return e}function ud(e,t){do{var n=Te;try{if(Qo(),cs.current=ps,ds){for(var s=ke.memoizedState;s!==null;){var u=s.queue;u!==null&&(u.pending=null),s=s.next}ds=!1}if(jn=0,Ne=Re=ke=null,ti=!1,ni=0,Tl.current=null,n===null||n.return===null){Oe=1,li=t,Te=null;break}e:{var d=e,m=n.return,y=n,_=t;if(t=Ue,y.flags|=32768,_!==null&&typeof _=="object"&&typeof _.then=="function"){var T=_,b=y,U=b.tag;if((b.mode&1)===0&&(U===0||U===11||U===15)){var I=b.alternate;I?(b.updateQueue=I.updateQueue,b.memoizedState=I.memoizedState,b.lanes=I.lanes):(b.updateQueue=null,b.memoizedState=null)}var F=Ic(m);if(F!==null){F.flags&=-257,Ac(F,m,y,d,t),F.mode&1&&$c(d,T,t),t=F,_=T;var q=t.updateQueue;if(q===null){var V=new Set;V.add(_),t.updateQueue=V}else q.add(_);break e}else{if((t&1)===0){$c(d,T,t),Dl();break e}_=Error(i(426))}}else if(we&&y.mode&1){var Pe=Ic(m);if(Pe!==null){(Pe.flags&65536)===0&&(Pe.flags|=256),Ac(Pe,m,y,d,t),Jo(sr(_,y));break e}}d=_=sr(_,y),Oe!==4&&(Oe=2),ai===null?ai=[d]:ai.push(d),d=m;do{switch(d.tag){case 3:d.flags|=65536,t&=-t,d.lanes|=t;var C=Nc(d,_,t);ic(d,C);break e;case 1:y=_;var k=d.type,P=d.stateNode;if((d.flags&128)===0&&(typeof k.getDerivedStateFromError=="function"||P!==null&&typeof P.componentDidCatch=="function"&&(un===null||!un.has(P)))){d.flags|=65536,t&=-t,d.lanes|=t;var z=Lc(d,y,t);ic(d,z);break e}}d=d.return}while(d!==null)}fd(n)}catch(K){t=K,Te===n&&n!==null&&(Te=n=n.return);continue}break}while(!0)}function cd(){var e=_s.current;return _s.current=ps,e===null?ps:e}function Dl(){(Oe===0||Oe===3||Oe===2)&&(Oe=4),Le===null||(Tn&268435455)===0&&(ks&268435455)===0||fn(Le,Ue)}function js(e,t){var n=le;le|=2;var s=cd();(Le!==e||Ue!==t)&&(Wt=null,On(e,t));do try{jp();break}catch(u){ud(e,u)}while(!0);if(Qo(),le=n,_s.current=s,Te!==null)throw Error(i(261));return Le=null,Ue=0,Oe}function jp(){for(;Te!==null;)dd(Te)}function Tp(){for(;Te!==null&&!Zf();)dd(Te)}function dd(e){var t=md(e.alternate,e,lt);e.memoizedProps=e.pendingProps,t===null?fd(e):Te=t,Tl.current=null}function fd(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=_p(n,t,lt),n!==null){Te=n;return}}else{if(n=kp(n,t),n!==null){n.flags&=32767,Te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Oe=6,Te=null;return}}if(t=t.sibling,t!==null){Te=t;return}Te=t=e}while(t!==null);Oe===0&&(Oe=5)}function Nn(e,t,n){var s=fe,u=ht.transition;try{ht.transition=null,fe=1,Rp(e,t,n,s)}finally{ht.transition=u,fe=s}return null}function Rp(e,t,n,s){do ur();while(cn!==null);if((le&6)!==0)throw Error(i(327));n=e.finishedWork;var u=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var d=n.lanes|n.childLanes;if(uh(e,d),e===Le&&(Te=Le=null,Ue=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||Es||(Es=!0,gd(Ni,function(){return ur(),null})),d=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||d){d=ht.transition,ht.transition=null;var m=fe;fe=1;var y=le;le|=4,Tl.current=null,Ep(e,n),nd(n,e),Gh(Do),Ui=!!bo,Do=bo=null,e.current=n,xp(n),eh(),le=y,fe=m,ht.transition=d}else e.current=n;if(Es&&(Es=!1,cn=e,xs=u),d=e.pendingLanes,d===0&&(un=null),rh(n.stateNode),Xe(e,Ce()),t!==null)for(s=e.onRecoverableError,n=0;n<t.length;n++)u=t[n],s(u.value,{componentStack:u.stack,digest:u.digest});if(Ss)throw Ss=!1,e=Nl,Nl=null,e;return(xs&1)!==0&&e.tag!==0&&ur(),d=e.pendingLanes,(d&1)!==0?e===Ll?ui++:(ui=0,Ll=e):ui=0,sn(),null}function ur(){if(cn!==null){var e=eu(xs),t=ht.transition,n=fe;try{if(ht.transition=null,fe=16>e?16:e,cn===null)var s=!1;else{if(e=cn,cn=null,xs=0,(le&6)!==0)throw Error(i(331));var u=le;for(le|=4,B=e.current;B!==null;){var d=B,m=d.child;if((B.flags&16)!==0){var y=d.deletions;if(y!==null){for(var _=0;_<y.length;_++){var T=y[_];for(B=T;B!==null;){var b=B;switch(b.tag){case 0:case 11:case 15:oi(8,b,d)}var U=b.child;if(U!==null)U.return=b,B=U;else for(;B!==null;){b=B;var I=b.sibling,F=b.return;if(Yc(b),b===T){B=null;break}if(I!==null){I.return=F,B=I;break}B=F}}}var q=d.alternate;if(q!==null){var V=q.child;if(V!==null){q.child=null;do{var Pe=V.sibling;V.sibling=null,V=Pe}while(V!==null)}}B=d}}if((d.subtreeFlags&2064)!==0&&m!==null)m.return=d,B=m;else e:for(;B!==null;){if(d=B,(d.flags&2048)!==0)switch(d.tag){case 0:case 11:case 15:oi(9,d,d.return)}var C=d.sibling;if(C!==null){C.return=d.return,B=C;break e}B=d.return}}var k=e.current;for(B=k;B!==null;){m=B;var P=m.child;if((m.subtreeFlags&2064)!==0&&P!==null)P.return=m,B=P;else e:for(m=k;B!==null;){if(y=B,(y.flags&2048)!==0)try{switch(y.tag){case 0:case 11:case 15:ws(9,y)}}catch(K){xe(y,y.return,K)}if(y===m){B=null;break e}var z=y.sibling;if(z!==null){z.return=y.return,B=z;break e}B=y.return}}if(le=u,sn(),Tt&&typeof Tt.onPostCommitFiberRoot=="function")try{Tt.onPostCommitFiberRoot(Li,e)}catch{}s=!0}return s}finally{fe=n,ht.transition=t}}return!1}function hd(e,t,n){t=sr(n,t),t=Nc(e,t,1),e=ln(e,t,1),t=Ve(),e!==null&&($r(e,1,t),Xe(e,t))}function xe(e,t,n){if(e.tag===3)hd(e,e,n);else for(;t!==null;){if(t.tag===3){hd(t,e,n);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(un===null||!un.has(s))){e=sr(n,e),e=Lc(t,e,1),t=ln(t,e,1),e=Ve(),t!==null&&($r(t,1,e),Xe(t,e));break}}t=t.return}}function Op(e,t,n){var s=e.pingCache;s!==null&&s.delete(t),t=Ve(),e.pingedLanes|=e.suspendedLanes&n,Le===e&&(Ue&n)===n&&(Oe===4||Oe===3&&(Ue&130023424)===Ue&&500>Ce()-Ol?On(e,0):Rl|=n),Xe(e,t)}function pd(e,t){t===0&&((e.mode&1)===0?t=1:(t=Ii,Ii<<=1,(Ii&130023424)===0&&(Ii=4194304)));var n=Ve();e=Mt(e,t),e!==null&&($r(e,t,n),Xe(e,n))}function Np(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),pd(e,n)}function Lp(e,t){var n=0;switch(e.tag){case 13:var s=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(i(314))}s!==null&&s.delete(t),pd(e,n)}var md;md=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Je.current)Qe=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return Qe=!1,wp(e,t,n);Qe=(e.flags&131072)!==0}else Qe=!1,we&&(t.flags&1048576)!==0&&Ju(t,ns,t.index);switch(t.lanes=0,t.tag){case 2:var s=t.type;vs(e,t),e=t.pendingProps;var u=Yn(t,Me.current);rr(t,n),u=ll(null,t,s,e,u,n);var d=al();return t.flags|=1,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ge(s)?(d=!0,Zi(t)):d=!1,t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,el(t),u.updater=ms,t.stateNode=u,u._reactInternals=t,pl(t,s,e,n),t=yl(null,t,s,!0,d,n)):(t.tag=0,we&&d&&Ho(t),qe(null,t,u,n),t=t.child),t;case 16:s=t.elementType;e:{switch(vs(e,t),e=t.pendingProps,u=s._init,s=u(s._payload),t.type=s,u=t.tag=Ip(s),e=wt(s,e),u){case 0:t=vl(null,t,s,e,n);break e;case 1:t=Fc(null,t,s,e,n);break e;case 11:t=bc(null,t,s,e,n);break e;case 14:t=Dc(null,t,s,wt(s.type,e),n);break e}throw Error(i(306,s,""))}return t;case 0:return s=t.type,u=t.pendingProps,u=t.elementType===s?u:wt(s,u),vl(e,t,s,u,n);case 1:return s=t.type,u=t.pendingProps,u=t.elementType===s?u:wt(s,u),Fc(e,t,s,u,n);case 3:e:{if(Bc(t),e===null)throw Error(i(387));s=t.pendingProps,d=t.memoizedState,u=d.element,rc(e,t),as(t,s,null,n);var m=t.memoizedState;if(s=m.element,d.isDehydrated)if(d={element:s,isDehydrated:!1,cache:m.cache,pendingSuspenseBoundaries:m.pendingSuspenseBoundaries,transitions:m.transitions},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){u=sr(Error(i(423)),t),t=Wc(e,t,s,n,u);break e}else if(s!==u){u=sr(Error(i(424)),t),t=Wc(e,t,s,n,u);break e}else for(ot=tn(t.stateNode.containerInfo.firstChild),st=t,we=!0,yt=null,n=tc(t,null,s,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(er(),s===u){t=Bt(e,t,n);break e}qe(e,t,s,n)}t=t.child}return t;case 5:return oc(t),e===null&&Ko(t),s=t.type,u=t.pendingProps,d=e!==null?e.memoizedProps:null,m=u.children,Uo(s,u)?m=null:d!==null&&Uo(s,d)&&(t.flags|=32),Mc(e,t),qe(e,t,m,n),t.child;case 6:return e===null&&Ko(t),null;case 13:return Hc(e,t,n);case 4:return tl(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=tr(t,null,s,n):qe(e,t,s,n),t.child;case 11:return s=t.type,u=t.pendingProps,u=t.elementType===s?u:wt(s,u),bc(e,t,s,u,n);case 7:return qe(e,t,t.pendingProps,n),t.child;case 8:return qe(e,t,t.pendingProps.children,n),t.child;case 12:return qe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(s=t.type._context,u=t.pendingProps,d=t.memoizedProps,m=u.value,pe(ss,s._currentValue),s._currentValue=m,d!==null)if(vt(d.value,m)){if(d.children===u.children&&!Je.current){t=Bt(e,t,n);break e}}else for(d=t.child,d!==null&&(d.return=t);d!==null;){var y=d.dependencies;if(y!==null){m=d.child;for(var _=y.firstContext;_!==null;){if(_.context===s){if(d.tag===1){_=Ft(-1,n&-n),_.tag=2;var T=d.updateQueue;if(T!==null){T=T.shared;var b=T.pending;b===null?_.next=_:(_.next=b.next,b.next=_),T.pending=_}}d.lanes|=n,_=d.alternate,_!==null&&(_.lanes|=n),Xo(d.return,n,t),y.lanes|=n;break}_=_.next}}else if(d.tag===10)m=d.type===t.type?null:d.child;else if(d.tag===18){if(m=d.return,m===null)throw Error(i(341));m.lanes|=n,y=m.alternate,y!==null&&(y.lanes|=n),Xo(m,n,t),m=d.sibling}else m=d.child;if(m!==null)m.return=d;else for(m=d;m!==null;){if(m===t){m=null;break}if(d=m.sibling,d!==null){d.return=m.return,m=d;break}m=m.return}d=m}qe(e,t,u.children,n),t=t.child}return t;case 9:return u=t.type,s=t.pendingProps.children,rr(t,n),u=dt(u),s=s(u),t.flags|=1,qe(e,t,s,n),t.child;case 14:return s=t.type,u=wt(s,t.pendingProps),u=wt(s.type,u),Dc(e,t,s,u,n);case 15:return Uc(e,t,t.type,t.pendingProps,n);case 17:return s=t.type,u=t.pendingProps,u=t.elementType===s?u:wt(s,u),vs(e,t),t.tag=1,Ge(s)?(e=!0,Zi(t)):e=!1,rr(t,n),Rc(t,s,u),pl(t,s,u,n),yl(null,t,s,!0,e,n);case 19:return Vc(e,t,n);case 22:return zc(e,t,n)}throw Error(i(156,t.tag))};function gd(e,t){return Ga(e,t)}function $p(e,t,n,s){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pt(e,t,n,s){return new $p(e,t,n,s)}function Ul(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ip(e){if(typeof e=="function")return Ul(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Pt)return 11;if(e===jt)return 14}return 2}function hn(e,t){var n=e.alternate;return n===null?(n=pt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ts(e,t,n,s,u,d){var m=2;if(s=e,typeof e=="function")Ul(e)&&(m=1);else if(typeof e=="string")m=5;else e:switch(e){case re:return Ln(n.children,u,d,t);case be:m=8,u|=8;break;case je:return e=pt(12,n,t,u|2),e.elementType=je,e.lanes=d,e;case nt:return e=pt(13,n,t,u),e.elementType=nt,e.lanes=d,e;case mt:return e=pt(19,n,t,u),e.elementType=mt,e.lanes=d,e;case Ee:return Rs(n,u,d,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ze:m=10;break e;case Ct:m=9;break e;case Pt:m=11;break e;case jt:m=14;break e;case Ke:m=16,s=null;break e}throw Error(i(130,e==null?e:typeof e,""))}return t=pt(m,n,t,u),t.elementType=e,t.type=s,t.lanes=d,t}function Ln(e,t,n,s){return e=pt(7,e,s,t),e.lanes=n,e}function Rs(e,t,n,s){return e=pt(22,e,s,t),e.elementType=Ee,e.lanes=n,e.stateNode={isHidden:!1},e}function zl(e,t,n){return e=pt(6,e,null,t),e.lanes=n,e}function Ml(e,t,n){return t=pt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ap(e,t,n,s,u){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ho(0),this.expirationTimes=ho(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ho(0),this.identifierPrefix=s,this.onRecoverableError=u,this.mutableSourceEagerHydrationData=null}function Fl(e,t,n,s,u,d,m,y,_){return e=new Ap(e,t,n,y,_),t===1?(t=1,d===!0&&(t|=8)):t=0,d=pt(3,null,null,t),e.current=d,d.stateNode=e,d.memoizedState={element:s,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},el(d),e}function bp(e,t,n){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ee,key:s==null?null:""+s,children:e,containerInfo:t,implementation:n}}function vd(e){if(!e)return rn;e=e._reactInternals;e:{if(_n(e)!==e||e.tag!==1)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ge(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(i(171))}if(e.tag===1){var n=e.type;if(Ge(n))return qu(e,n,t)}return t}function yd(e,t,n,s,u,d,m,y,_){return e=Fl(n,s,!0,e,u,d,m,y,_),e.context=vd(null),n=e.current,s=Ve(),u=dn(n),d=Ft(s,u),d.callback=t??null,ln(n,d,u),e.current.lanes=u,$r(e,u,s),Xe(e,s),e}function Os(e,t,n,s){var u=t.current,d=Ve(),m=dn(u);return n=vd(n),t.context===null?t.context=n:t.pendingContext=n,t=Ft(d,m),t.payload={element:e},s=s===void 0?null:s,s!==null&&(t.callback=s),e=ln(u,t,m),e!==null&&(St(e,u,m,d),ls(e,u,m)),m}function Ns(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function wd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bl(e,t){wd(e,t),(e=e.alternate)&&wd(e,t)}function Dp(){return null}var _d=typeof reportError=="function"?reportError:function(e){console.error(e)};function Wl(e){this._internalRoot=e}Ls.prototype.render=Wl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));Os(e,t,null,null)},Ls.prototype.unmount=Wl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Rn(function(){Os(null,e,null,null)}),t[bt]=null}};function Ls(e){this._internalRoot=e}Ls.prototype.unstable_scheduleHydration=function(e){if(e){var t=ru();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Xt.length&&t!==0&&t<Xt[n].priority;n++);Xt.splice(n,0,e),n===0&&ou(e)}};function Hl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function $s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function kd(){}function Up(e,t,n,s,u){if(u){if(typeof s=="function"){var d=s;s=function(){var T=Ns(m);d.call(T)}}var m=yd(t,s,e,0,null,!1,!1,"",kd);return e._reactRootContainer=m,e[bt]=m.current,Kr(e.nodeType===8?e.parentNode:e),Rn(),m}for(;u=e.lastChild;)e.removeChild(u);if(typeof s=="function"){var y=s;s=function(){var T=Ns(_);y.call(T)}}var _=Fl(e,0,!1,null,null,!1,!1,"",kd);return e._reactRootContainer=_,e[bt]=_.current,Kr(e.nodeType===8?e.parentNode:e),Rn(function(){Os(t,_,n,s)}),_}function Is(e,t,n,s,u){var d=n._reactRootContainer;if(d){var m=d;if(typeof u=="function"){var y=u;u=function(){var _=Ns(m);y.call(_)}}Os(t,m,e,u)}else m=Up(n,t,e,u,s);return Ns(m)}tu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Lr(t.pendingLanes);n!==0&&(po(t,n|1),Xe(t,Ce()),(le&6)===0&&(ar=Ce()+500,sn()))}break;case 13:Rn(function(){var s=Mt(e,1);if(s!==null){var u=Ve();St(s,e,1,u)}}),Bl(e,1)}},mo=function(e){if(e.tag===13){var t=Mt(e,134217728);if(t!==null){var n=Ve();St(t,e,134217728,n)}Bl(e,134217728)}},nu=function(e){if(e.tag===13){var t=dn(e),n=Mt(e,t);if(n!==null){var s=Ve();St(n,e,t,s)}Bl(e,t)}},ru=function(){return fe},iu=function(e,t){var n=fe;try{return fe=e,t()}finally{fe=n}},oo=function(e,t,n){switch(t){case"input":if(Xs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var s=n[t];if(s!==e&&s.form===e.form){var u=Yi(s);if(!u)throw Error(i(90));Ta(s),Xs(s,u)}}}break;case"textarea":$a(e,n);break;case"select":t=n.value,t!=null&&zn(e,!!n.multiple,t,!1)}},Ba=Al,Wa=Rn;var zp={usingClientEntryPoint:!1,Events:[Qr,Gn,Yi,Ma,Fa,Al]},ci={findFiberByHostInstance:kn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Mp={bundleType:ci.bundleType,version:ci.version,rendererPackageName:ci.rendererPackageName,rendererConfig:ci.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:W.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ka(e),e===null?null:e.stateNode},findFiberByHostInstance:ci.findFiberByHostInstance||Dp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var As=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!As.isDisabled&&As.supportsFiber)try{Li=As.inject(Mp),Tt=As}catch{}}return Ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=zp,Ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Hl(t))throw Error(i(200));return bp(e,t,null,n)},Ze.createRoot=function(e,t){if(!Hl(e))throw Error(i(299));var n=!1,s="",u=_d;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onRecoverableError!==void 0&&(u=t.onRecoverableError)),t=Fl(e,1,!1,null,null,n,!1,s,u),e[bt]=t.current,Kr(e.nodeType===8?e.parentNode:e),new Wl(t)},Ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=Ka(t),e=e===null?null:e.stateNode,e},Ze.flushSync=function(e){return Rn(e)},Ze.hydrate=function(e,t,n){if(!$s(t))throw Error(i(200));return Is(null,e,t,!0,n)},Ze.hydrateRoot=function(e,t,n){if(!Hl(e))throw Error(i(405));var s=n!=null&&n.hydratedSources||null,u=!1,d="",m=_d;if(n!=null&&(n.unstable_strictMode===!0&&(u=!0),n.identifierPrefix!==void 0&&(d=n.identifierPrefix),n.onRecoverableError!==void 0&&(m=n.onRecoverableError)),t=yd(t,null,e,1,n??null,u,!1,d,m),e[bt]=t.current,Kr(e),s)for(e=0;e<s.length;e++)n=s[e],u=n._getVersion,u=u(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,u]:t.mutableSourceEagerHydrationData.push(n,u);return new Ls(t)},Ze.render=function(e,t,n){if(!$s(t))throw Error(i(200));return Is(null,e,t,!1,n)},Ze.unmountComponentAtNode=function(e){if(!$s(e))throw Error(i(40));return e._reactRootContainer?(Rn(function(){Is(null,null,e,!1,function(){e._reactRootContainer=null,e[bt]=null})}),!0):!1},Ze.unstable_batchedUpdates=Al,Ze.unstable_renderSubtreeIntoContainer=function(e,t,n,s){if(!$s(n))throw Error(i(200));if(e==null||e._reactInternals===void 0)throw Error(i(38));return Is(e,t,n,!1,s)},Ze.version="18.3.1-next-f1338f8080-20240426",Ze}var Rd;function Qp(){if(Rd)return Kl.exports;Rd=1;function o(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(o)}catch(r){console.error(r)}}return o(),Kl.exports=Gp(),Kl.exports}var Od;function Yp(){if(Od)return bs;Od=1;var o=Qp();return bs.createRoot=o.createRoot,bs.hydrateRoot=o.hydrateRoot,bs}var Xp=Yp();/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Nd="popstate";function Zp(o={}){function r(l,a){let{pathname:c,search:f,hash:h}=l.location;return sa("",{pathname:c,search:f,hash:h},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function i(l,a){return typeof a=="string"?a:yi(a)}return tm(r,i,null,o)}function Se(o,r){if(o===!1||o===null||typeof o>"u")throw new Error(r)}function $t(o,r){if(!o){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function em(){return Math.random().toString(36).substring(2,10)}function Ld(o,r){return{usr:o.state,key:o.key,idx:r}}function sa(o,r,i=null,l){return{pathname:typeof o=="string"?o:o.pathname,search:"",hash:"",...typeof r=="string"?Er(r):r,state:i,key:r&&r.key||l||em()}}function yi({pathname:o="/",search:r="",hash:i=""}){return r&&r!=="?"&&(o+=r.charAt(0)==="?"?r:"?"+r),i&&i!=="#"&&(o+=i.charAt(0)==="#"?i:"#"+i),o}function Er(o){let r={};if(o){let i=o.indexOf("#");i>=0&&(r.hash=o.substring(i),o=o.substring(0,i));let l=o.indexOf("?");l>=0&&(r.search=o.substring(l),o=o.substring(0,l)),o&&(r.pathname=o)}return r}function tm(o,r,i,l={}){let{window:a=document.defaultView,v5Compat:c=!1}=l,f=a.history,h="POP",p=null,g=v();g==null&&(g=0,f.replaceState({...f.state,idx:g},""));function v(){return(f.state||{idx:null}).idx}function w(){h="POP";let E=v(),N=E==null?null:E-g;g=E,p&&p({action:h,location:$.location,delta:N})}function S(E,N){h="PUSH";let J=sa($.location,E,N);g=v()+1;let D=Ld(J,g),W=$.createHref(J);try{f.pushState(D,"",W)}catch(G){if(G instanceof DOMException&&G.name==="DataCloneError")throw G;a.location.assign(W)}c&&p&&p({action:h,location:$.location,delta:1})}function j(E,N){h="REPLACE";let J=sa($.location,E,N);g=v();let D=Ld(J,g),W=$.createHref(J);f.replaceState(D,"",W),c&&p&&p({action:h,location:$.location,delta:0})}function O(E){return nm(E)}let $={get action(){return h},get location(){return o(a,f)},listen(E){if(p)throw new Error("A history only accepts one active listener");return a.addEventListener(Nd,w),p=E,()=>{a.removeEventListener(Nd,w),p=null}},createHref(E){return r(a,E)},createURL:O,encodeLocation(E){let N=O(E);return{pathname:N.pathname,search:N.search,hash:N.hash}},push:S,replace:j,go(E){return f.go(E)}};return $}function nm(o,r=!1){let i="http://localhost";typeof window<"u"&&(i=window.location.origin!=="null"?window.location.origin:window.location.href),Se(i,"No window.location.(origin|href) available to create URL");let l=typeof o=="string"?o:yi(o);return l=l.replace(/ $/,"%20"),!r&&l.startsWith("//")&&(l=i+l),new URL(l,i)}function df(o,r,i="/"){return rm(o,r,i,!1)}function rm(o,r,i,l){let a=typeof r=="string"?Er(r):r,c=Jt(a.pathname||"/",i);if(c==null)return null;let f=ff(o);im(f);let h=null;for(let p=0;h==null&&p<f.length;++p){let g=mm(c);h=hm(f[p],g,l)}return h}function ff(o,r=[],i=[],l=""){let a=(c,f,h)=>{let p={relativePath:h===void 0?c.path||"":h,caseSensitive:c.caseSensitive===!0,childrenIndex:f,route:c};p.relativePath.startsWith("/")&&(Se(p.relativePath.startsWith(l),`Absolute route path "${p.relativePath}" nested under path "${l}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(l.length));let g=Kt([l,p.relativePath]),v=i.concat(p);c.children&&c.children.length>0&&(Se(c.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${g}".`),ff(c.children,r,v,g)),!(c.path==null&&!c.index)&&r.push({path:g,score:dm(g,c.index),routesMeta:v})};return o.forEach((c,f)=>{if(c.path===""||!c.path?.includes("?"))a(c,f);else for(let h of hf(c.path))a(c,f,h)}),r}function hf(o){let r=o.split("/");if(r.length===0)return[];let[i,...l]=r,a=i.endsWith("?"),c=i.replace(/\?$/,"");if(l.length===0)return a?[c,""]:[c];let f=hf(l.join("/")),h=[];return h.push(...f.map(p=>p===""?c:[c,p].join("/"))),a&&h.push(...f),h.map(p=>o.startsWith("/")&&p===""?"/":p)}function im(o){o.sort((r,i)=>r.score!==i.score?i.score-r.score:fm(r.routesMeta.map(l=>l.childrenIndex),i.routesMeta.map(l=>l.childrenIndex)))}var sm=/^:[\w-]+$/,om=3,lm=2,am=1,um=10,cm=-2,$d=o=>o==="*";function dm(o,r){let i=o.split("/"),l=i.length;return i.some($d)&&(l+=cm),r&&(l+=lm),i.filter(a=>!$d(a)).reduce((a,c)=>a+(sm.test(c)?om:c===""?am:um),l)}function fm(o,r){return o.length===r.length&&o.slice(0,-1).every((l,a)=>l===r[a])?o[o.length-1]-r[r.length-1]:0}function hm(o,r,i=!1){let{routesMeta:l}=o,a={},c="/",f=[];for(let h=0;h<l.length;++h){let p=l[h],g=h===l.length-1,v=c==="/"?r:r.slice(c.length)||"/",w=Hs({path:p.relativePath,caseSensitive:p.caseSensitive,end:g},v),S=p.route;if(!w&&g&&i&&!l[l.length-1].route.index&&(w=Hs({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},v)),!w)return null;Object.assign(a,w.params),f.push({params:a,pathname:Kt([c,w.pathname]),pathnameBase:wm(Kt([c,w.pathnameBase])),route:S}),w.pathnameBase!=="/"&&(c=Kt([c,w.pathnameBase]))}return f}function Hs(o,r){typeof o=="string"&&(o={path:o,caseSensitive:!1,end:!0});let[i,l]=pm(o.path,o.caseSensitive,o.end),a=r.match(i);if(!a)return null;let c=a[0],f=c.replace(/(.)\/+$/,"$1"),h=a.slice(1);return{params:l.reduce((g,{paramName:v,isOptional:w},S)=>{if(v==="*"){let O=h[S]||"";f=c.slice(0,c.length-O.length).replace(/(.)\/+$/,"$1")}const j=h[S];return w&&!j?g[v]=void 0:g[v]=(j||"").replace(/%2F/g,"/"),g},{}),pathname:c,pathnameBase:f,pattern:o}}function pm(o,r=!1,i=!0){$t(o==="*"||!o.endsWith("*")||o.endsWith("/*"),`Route path "${o}" will be treated as if it were "${o.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${o.replace(/\*$/,"/*")}".`);let l=[],a="^"+o.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(f,h,p)=>(l.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return o.endsWith("*")?(l.push({paramName:"*"}),a+=o==="*"||o==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):i?a+="\\/*$":o!==""&&o!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,r?void 0:"i"),l]}function mm(o){try{return o.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return $t(!1,`The URL path "${o}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),o}}function Jt(o,r){if(r==="/")return o;if(!o.toLowerCase().startsWith(r.toLowerCase()))return null;let i=r.endsWith("/")?r.length-1:r.length,l=o.charAt(i);return l&&l!=="/"?null:o.slice(i)||"/"}function gm(o,r="/"){let{pathname:i,search:l="",hash:a=""}=typeof o=="string"?Er(o):o;return{pathname:i?i.startsWith("/")?i:vm(i,r):r,search:_m(l),hash:km(a)}}function vm(o,r){let i=r.replace(/\/+$/,"").split("/");return o.split("/").forEach(a=>{a===".."?i.length>1&&i.pop():a!=="."&&i.push(a)}),i.length>1?i.join("/"):"/"}function Ql(o,r,i,l){return`Cannot include a '${o}' character in a manually specified \`to.${r}\` field [${JSON.stringify(l)}].  Please separate it out to the \`to.${i}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function ym(o){return o.filter((r,i)=>i===0||r.route.path&&r.route.path.length>0)}function pf(o){let r=ym(o);return r.map((i,l)=>l===r.length-1?i.pathname:i.pathnameBase)}function mf(o,r,i,l=!1){let a;typeof o=="string"?a=Er(o):(a={...o},Se(!a.pathname||!a.pathname.includes("?"),Ql("?","pathname","search",a)),Se(!a.pathname||!a.pathname.includes("#"),Ql("#","pathname","hash",a)),Se(!a.search||!a.search.includes("#"),Ql("#","search","hash",a)));let c=o===""||a.pathname==="",f=c?"/":a.pathname,h;if(f==null)h=i;else{let w=r.length-1;if(!l&&f.startsWith("..")){let S=f.split("/");for(;S[0]==="..";)S.shift(),w-=1;a.pathname=S.join("/")}h=w>=0?r[w]:"/"}let p=gm(a,h),g=f&&f!=="/"&&f.endsWith("/"),v=(c||f===".")&&i.endsWith("/");return!p.pathname.endsWith("/")&&(g||v)&&(p.pathname+="/"),p}var Kt=o=>o.join("/").replace(/\/\/+/g,"/"),wm=o=>o.replace(/\/+$/,"").replace(/^\/*/,"/"),_m=o=>!o||o==="?"?"":o.startsWith("?")?o:"?"+o,km=o=>!o||o==="#"?"":o.startsWith("#")?o:"#"+o;function Sm(o){return o!=null&&typeof o.status=="number"&&typeof o.statusText=="string"&&typeof o.internal=="boolean"&&"data"in o}var gf=["POST","PUT","PATCH","DELETE"];new Set(gf);var Em=["GET",...gf];new Set(Em);var xr=R.createContext(null);xr.displayName="DataRouter";var Ks=R.createContext(null);Ks.displayName="DataRouterState";R.createContext(!1);var vf=R.createContext({isTransitioning:!1});vf.displayName="ViewTransition";var xm=R.createContext(new Map);xm.displayName="Fetchers";var Cm=R.createContext(null);Cm.displayName="Await";var It=R.createContext(null);It.displayName="Navigation";var ki=R.createContext(null);ki.displayName="Location";var At=R.createContext({outlet:null,matches:[],isDataRoute:!1});At.displayName="Route";var ya=R.createContext(null);ya.displayName="RouteError";function Pm(o,{relative:r}={}){Se(Si(),"useHref() may be used only in the context of a <Router> component.");let{basename:i,navigator:l}=R.useContext(It),{hash:a,pathname:c,search:f}=Ei(o,{relative:r}),h=c;return i!=="/"&&(h=c==="/"?i:Kt([i,c])),l.createHref({pathname:h,search:f,hash:a})}function Si(){return R.useContext(ki)!=null}function yn(){return Se(Si(),"useLocation() may be used only in the context of a <Router> component."),R.useContext(ki).location}var yf="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function wf(o){R.useContext(It).static||R.useLayoutEffect(o)}function jm(){let{isDataRoute:o}=R.useContext(At);return o?Mm():Tm()}function Tm(){Se(Si(),"useNavigate() may be used only in the context of a <Router> component.");let o=R.useContext(xr),{basename:r,navigator:i}=R.useContext(It),{matches:l}=R.useContext(At),{pathname:a}=yn(),c=JSON.stringify(pf(l)),f=R.useRef(!1);return wf(()=>{f.current=!0}),R.useCallback((p,g={})=>{if($t(f.current,yf),!f.current)return;if(typeof p=="number"){i.go(p);return}let v=mf(p,JSON.parse(c),a,g.relative==="path");o==null&&r!=="/"&&(v.pathname=v.pathname==="/"?r:Kt([r,v.pathname])),(g.replace?i.replace:i.push)(v,g.state,g)},[r,i,c,a,o])}R.createContext(null);function Js(){let{matches:o}=R.useContext(At),r=o[o.length-1];return r?r.params:{}}function Ei(o,{relative:r}={}){let{matches:i}=R.useContext(At),{pathname:l}=yn(),a=JSON.stringify(pf(i));return R.useMemo(()=>mf(o,JSON.parse(a),l,r==="path"),[o,a,l,r])}function Rm(o,r){return _f(o,r)}function _f(o,r,i,l){Se(Si(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=R.useContext(It),{matches:c}=R.useContext(At),f=c[c.length-1],h=f?f.params:{},p=f?f.pathname:"/",g=f?f.pathnameBase:"/",v=f&&f.route;{let N=v&&v.path||"";kf(p,!v||N.endsWith("*")||N.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${N}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${N}"> to <Route path="${N==="/"?"*":`${N}/*`}">.`)}let w=yn(),S;if(r){let N=typeof r=="string"?Er(r):r;Se(g==="/"||N.pathname?.startsWith(g),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${g}" but pathname "${N.pathname}" was given in the \`location\` prop.`),S=N}else S=w;let j=S.pathname||"/",O=j;if(g!=="/"){let N=g.replace(/^\//,"").split("/");O="/"+j.replace(/^\//,"").split("/").slice(N.length).join("/")}let $=df(o,{pathname:O});$t(v||$!=null,`No routes matched location "${S.pathname}${S.search}${S.hash}" `),$t($==null||$[$.length-1].route.element!==void 0||$[$.length-1].route.Component!==void 0||$[$.length-1].route.lazy!==void 0,`Matched leaf route at location "${S.pathname}${S.search}${S.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let E=Im($&&$.map(N=>Object.assign({},N,{params:Object.assign({},h,N.params),pathname:Kt([g,a.encodeLocation?a.encodeLocation(N.pathname).pathname:N.pathname]),pathnameBase:N.pathnameBase==="/"?g:Kt([g,a.encodeLocation?a.encodeLocation(N.pathnameBase).pathname:N.pathnameBase])})),c,i,l);return r&&E?R.createElement(ki.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...S},navigationType:"POP"}},E):E}function Om(){let o=zm(),r=Sm(o)?`${o.status} ${o.statusText}`:o instanceof Error?o.message:JSON.stringify(o),i=o instanceof Error?o.stack:null,l="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:l},c={padding:"2px 4px",backgroundColor:l},f=null;return console.error("Error handled by React Router default ErrorBoundary:",o),f=R.createElement(R.Fragment,null,R.createElement("p",null,"💿 Hey developer 👋"),R.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",R.createElement("code",{style:c},"ErrorBoundary")," or"," ",R.createElement("code",{style:c},"errorElement")," prop on your route.")),R.createElement(R.Fragment,null,R.createElement("h2",null,"Unexpected Application Error!"),R.createElement("h3",{style:{fontStyle:"italic"}},r),i?R.createElement("pre",{style:a},i):null,f)}var Nm=R.createElement(Om,null),Lm=class extends R.Component{constructor(o){super(o),this.state={location:o.location,revalidation:o.revalidation,error:o.error}}static getDerivedStateFromError(o){return{error:o}}static getDerivedStateFromProps(o,r){return r.location!==o.location||r.revalidation!=="idle"&&o.revalidation==="idle"?{error:o.error,location:o.location,revalidation:o.revalidation}:{error:o.error!==void 0?o.error:r.error,location:r.location,revalidation:o.revalidation||r.revalidation}}componentDidCatch(o,r){console.error("React Router caught the following error during render",o,r)}render(){return this.state.error!==void 0?R.createElement(At.Provider,{value:this.props.routeContext},R.createElement(ya.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function $m({routeContext:o,match:r,children:i}){let l=R.useContext(xr);return l&&l.static&&l.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=r.route.id),R.createElement(At.Provider,{value:o},i)}function Im(o,r=[],i=null,l=null){if(o==null){if(!i)return null;if(i.errors)o=i.matches;else if(r.length===0&&!i.initialized&&i.matches.length>0)o=i.matches;else return null}let a=o,c=i?.errors;if(c!=null){let p=a.findIndex(g=>g.route.id&&c?.[g.route.id]!==void 0);Se(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(c).join(",")}`),a=a.slice(0,Math.min(a.length,p+1))}let f=!1,h=-1;if(i)for(let p=0;p<a.length;p++){let g=a[p];if((g.route.HydrateFallback||g.route.hydrateFallbackElement)&&(h=p),g.route.id){let{loaderData:v,errors:w}=i,S=g.route.loader&&!v.hasOwnProperty(g.route.id)&&(!w||w[g.route.id]===void 0);if(g.route.lazy||S){f=!0,h>=0?a=a.slice(0,h+1):a=[a[0]];break}}}return a.reduceRight((p,g,v)=>{let w,S=!1,j=null,O=null;i&&(w=c&&g.route.id?c[g.route.id]:void 0,j=g.route.errorElement||Nm,f&&(h<0&&v===0?(kf("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),S=!0,O=null):h===v&&(S=!0,O=g.route.hydrateFallbackElement||null)));let $=r.concat(a.slice(0,v+1)),E=()=>{let N;return w?N=j:S?N=O:g.route.Component?N=R.createElement(g.route.Component,null):g.route.element?N=g.route.element:N=p,R.createElement($m,{match:g,routeContext:{outlet:p,matches:$,isDataRoute:i!=null},children:N})};return i&&(g.route.ErrorBoundary||g.route.errorElement||v===0)?R.createElement(Lm,{location:i.location,revalidation:i.revalidation,component:j,error:w,children:E(),routeContext:{outlet:null,matches:$,isDataRoute:!0}}):E()},null)}function wa(o){return`${o} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Am(o){let r=R.useContext(xr);return Se(r,wa(o)),r}function bm(o){let r=R.useContext(Ks);return Se(r,wa(o)),r}function Dm(o){let r=R.useContext(At);return Se(r,wa(o)),r}function _a(o){let r=Dm(o),i=r.matches[r.matches.length-1];return Se(i.route.id,`${o} can only be used on routes that contain a unique "id"`),i.route.id}function Um(){return _a("useRouteId")}function zm(){let o=R.useContext(ya),r=bm("useRouteError"),i=_a("useRouteError");return o!==void 0?o:r.errors?.[i]}function Mm(){let{router:o}=Am("useNavigate"),r=_a("useNavigate"),i=R.useRef(!1);return wf(()=>{i.current=!0}),R.useCallback(async(a,c={})=>{$t(i.current,yf),i.current&&(typeof a=="number"?o.navigate(a):await o.navigate(a,{fromRouteId:r,...c}))},[o,r])}var Id={};function kf(o,r,i){!r&&!Id[o]&&(Id[o]=!0,$t(!1,i))}R.memo(Fm);function Fm({routes:o,future:r,state:i}){return _f(o,void 0,i,r)}function An(o){Se(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Bm({basename:o="/",children:r=null,location:i,navigationType:l="POP",navigator:a,static:c=!1}){Se(!Si(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let f=o.replace(/^\/*/,"/"),h=R.useMemo(()=>({basename:f,navigator:a,static:c,future:{}}),[f,a,c]);typeof i=="string"&&(i=Er(i));let{pathname:p="/",search:g="",hash:v="",state:w=null,key:S="default"}=i,j=R.useMemo(()=>{let O=Jt(p,f);return O==null?null:{location:{pathname:O,search:g,hash:v,state:w,key:S},navigationType:l}},[f,p,g,v,w,S,l]);return $t(j!=null,`<Router basename="${f}"> is not able to match the URL "${p}${g}${v}" because it does not start with the basename, so the <Router> won't render anything.`),j==null?null:R.createElement(It.Provider,{value:h},R.createElement(ki.Provider,{children:r,value:j}))}function Wm({children:o,location:r}){return Rm(oa(o),r)}function oa(o,r=[]){let i=[];return R.Children.forEach(o,(l,a)=>{if(!R.isValidElement(l))return;let c=[...r,a];if(l.type===R.Fragment){i.push.apply(i,oa(l.props.children,c));return}Se(l.type===An,`[${typeof l.type=="string"?l.type:l.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Se(!l.props.index||!l.props.children,"An index route cannot have child routes.");let f={id:l.props.id||c.join("-"),caseSensitive:l.props.caseSensitive,element:l.props.element,Component:l.props.Component,index:l.props.index,path:l.props.path,loader:l.props.loader,action:l.props.action,hydrateFallbackElement:l.props.hydrateFallbackElement,HydrateFallback:l.props.HydrateFallback,errorElement:l.props.errorElement,ErrorBoundary:l.props.ErrorBoundary,hasErrorBoundary:l.props.hasErrorBoundary===!0||l.props.ErrorBoundary!=null||l.props.errorElement!=null,shouldRevalidate:l.props.shouldRevalidate,handle:l.props.handle,lazy:l.props.lazy};l.props.children&&(f.children=oa(l.props.children,c)),i.push(f)}),i}var Bs="get",Ws="application/x-www-form-urlencoded";function Gs(o){return o!=null&&typeof o.tagName=="string"}function Hm(o){return Gs(o)&&o.tagName.toLowerCase()==="button"}function qm(o){return Gs(o)&&o.tagName.toLowerCase()==="form"}function Vm(o){return Gs(o)&&o.tagName.toLowerCase()==="input"}function Km(o){return!!(o.metaKey||o.altKey||o.ctrlKey||o.shiftKey)}function Jm(o,r){return o.button===0&&(!r||r==="_self")&&!Km(o)}var Ds=null;function Gm(){if(Ds===null)try{new FormData(document.createElement("form"),0),Ds=!1}catch{Ds=!0}return Ds}var Qm=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Yl(o){return o!=null&&!Qm.has(o)?($t(!1,`"${o}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ws}"`),null):o}function Ym(o,r){let i,l,a,c,f;if(qm(o)){let h=o.getAttribute("action");l=h?Jt(h,r):null,i=o.getAttribute("method")||Bs,a=Yl(o.getAttribute("enctype"))||Ws,c=new FormData(o)}else if(Hm(o)||Vm(o)&&(o.type==="submit"||o.type==="image")){let h=o.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=o.getAttribute("formaction")||h.getAttribute("action");if(l=p?Jt(p,r):null,i=o.getAttribute("formmethod")||h.getAttribute("method")||Bs,a=Yl(o.getAttribute("formenctype"))||Yl(h.getAttribute("enctype"))||Ws,c=new FormData(h,o),!Gm()){let{name:g,type:v,value:w}=o;if(v==="image"){let S=g?`${g}.`:"";c.append(`${S}x`,"0"),c.append(`${S}y`,"0")}else g&&c.append(g,w)}}else{if(Gs(o))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');i=Bs,l=null,a=Ws,f=o}return c&&a==="text/plain"&&(f=c,c=void 0),{action:l,method:i.toLowerCase(),encType:a,formData:c,body:f}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function ka(o,r){if(o===!1||o===null||typeof o>"u")throw new Error(r)}function Xm(o,r,i){let l=typeof o=="string"?new URL(o,typeof window>"u"?"server://singlefetch/":window.location.origin):o;return l.pathname==="/"?l.pathname=`_root.${i}`:r&&Jt(l.pathname,r)==="/"?l.pathname=`${r.replace(/\/$/,"")}/_root.${i}`:l.pathname=`${l.pathname.replace(/\/$/,"")}.${i}`,l}async function Zm(o,r){if(o.id in r)return r[o.id];try{let i=await import(o.module);return r[o.id]=i,i}catch(i){return console.error(`Error loading route module \`${o.module}\`, reloading page...`),console.error(i),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function eg(o){return o==null?!1:o.href==null?o.rel==="preload"&&typeof o.imageSrcSet=="string"&&typeof o.imageSizes=="string":typeof o.rel=="string"&&typeof o.href=="string"}async function tg(o,r,i){let l=await Promise.all(o.map(async a=>{let c=r.routes[a.route.id];if(c){let f=await Zm(c,i);return f.links?f.links():[]}return[]}));return sg(l.flat(1).filter(eg).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function Ad(o,r,i,l,a,c){let f=(p,g)=>i[g]?p.route.id!==i[g].route.id:!0,h=(p,g)=>i[g].pathname!==p.pathname||i[g].route.path?.endsWith("*")&&i[g].params["*"]!==p.params["*"];return c==="assets"?r.filter((p,g)=>f(p,g)||h(p,g)):c==="data"?r.filter((p,g)=>{let v=l.routes[p.route.id];if(!v||!v.hasLoader)return!1;if(f(p,g)||h(p,g))return!0;if(p.route.shouldRevalidate){let w=p.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:i[0]?.params||{},nextUrl:new URL(o,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof w=="boolean")return w}return!0}):[]}function ng(o,r,{includeHydrateFallback:i}={}){return rg(o.map(l=>{let a=r.routes[l.route.id];if(!a)return[];let c=[a.module];return a.clientActionModule&&(c=c.concat(a.clientActionModule)),a.clientLoaderModule&&(c=c.concat(a.clientLoaderModule)),i&&a.hydrateFallbackModule&&(c=c.concat(a.hydrateFallbackModule)),a.imports&&(c=c.concat(a.imports)),c}).flat(1))}function rg(o){return[...new Set(o)]}function ig(o){let r={},i=Object.keys(o).sort();for(let l of i)r[l]=o[l];return r}function sg(o,r){let i=new Set;return new Set(r),o.reduce((l,a)=>{let c=JSON.stringify(ig(a));return i.has(c)||(i.add(c),l.push({key:c,link:a})),l},[])}function Sf(){let o=R.useContext(xr);return ka(o,"You must render this element inside a <DataRouterContext.Provider> element"),o}function og(){let o=R.useContext(Ks);return ka(o,"You must render this element inside a <DataRouterStateContext.Provider> element"),o}var Sa=R.createContext(void 0);Sa.displayName="FrameworkContext";function Ef(){let o=R.useContext(Sa);return ka(o,"You must render this element inside a <HydratedRouter> element"),o}function lg(o,r){let i=R.useContext(Sa),[l,a]=R.useState(!1),[c,f]=R.useState(!1),{onFocus:h,onBlur:p,onMouseEnter:g,onMouseLeave:v,onTouchStart:w}=r,S=R.useRef(null);R.useEffect(()=>{if(o==="render"&&f(!0),o==="viewport"){let $=N=>{N.forEach(J=>{f(J.isIntersecting)})},E=new IntersectionObserver($,{threshold:.5});return S.current&&E.observe(S.current),()=>{E.disconnect()}}},[o]),R.useEffect(()=>{if(l){let $=setTimeout(()=>{f(!0)},100);return()=>{clearTimeout($)}}},[l]);let j=()=>{a(!0)},O=()=>{a(!1),f(!1)};return i?o!=="intent"?[c,S,{}]:[c,S,{onFocus:fi(h,j),onBlur:fi(p,O),onMouseEnter:fi(g,j),onMouseLeave:fi(v,O),onTouchStart:fi(w,j)}]:[!1,S,{}]}function fi(o,r){return i=>{o&&o(i),i.defaultPrevented||r(i)}}function ag({page:o,...r}){let{router:i}=Sf(),l=R.useMemo(()=>df(i.routes,o,i.basename),[i.routes,o,i.basename]);return l?R.createElement(cg,{page:o,matches:l,...r}):null}function ug(o){let{manifest:r,routeModules:i}=Ef(),[l,a]=R.useState([]);return R.useEffect(()=>{let c=!1;return tg(o,r,i).then(f=>{c||a(f)}),()=>{c=!0}},[o,r,i]),l}function cg({page:o,matches:r,...i}){let l=yn(),{manifest:a,routeModules:c}=Ef(),{basename:f}=Sf(),{loaderData:h,matches:p}=og(),g=R.useMemo(()=>Ad(o,r,p,a,l,"data"),[o,r,p,a,l]),v=R.useMemo(()=>Ad(o,r,p,a,l,"assets"),[o,r,p,a,l]),w=R.useMemo(()=>{if(o===l.pathname+l.search+l.hash)return[];let O=new Set,$=!1;if(r.forEach(N=>{let J=a.routes[N.route.id];!J||!J.hasLoader||(!g.some(D=>D.route.id===N.route.id)&&N.route.id in h&&c[N.route.id]?.shouldRevalidate||J.hasClientLoader?$=!0:O.add(N.route.id))}),O.size===0)return[];let E=Xm(o,f,"data");return $&&O.size>0&&E.searchParams.set("_routes",r.filter(N=>O.has(N.route.id)).map(N=>N.route.id).join(",")),[E.pathname+E.search]},[f,h,l,a,g,r,o,c]),S=R.useMemo(()=>ng(v,a),[v,a]),j=ug(v);return R.createElement(R.Fragment,null,w.map(O=>R.createElement("link",{key:O,rel:"prefetch",as:"fetch",href:O,...i})),S.map(O=>R.createElement("link",{key:O,rel:"modulepreload",href:O,...i})),j.map(({key:O,link:$})=>R.createElement("link",{key:O,...$})))}function dg(...o){return r=>{o.forEach(i=>{typeof i=="function"?i(r):i!=null&&(i.current=r)})}}var xf=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{xf&&(window.__reactRouterVersion="7.7.1")}catch{}function fg({basename:o,children:r,window:i}){let l=R.useRef();l.current==null&&(l.current=Zp({window:i,v5Compat:!0}));let a=l.current,[c,f]=R.useState({action:a.action,location:a.location}),h=R.useCallback(p=>{R.startTransition(()=>f(p))},[f]);return R.useLayoutEffect(()=>a.listen(h),[a,h]),R.createElement(Bm,{basename:o,children:r,location:c.location,navigationType:c.action,navigator:a})}var Cf=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,tt=R.forwardRef(function({onClick:r,discover:i="render",prefetch:l="none",relative:a,reloadDocument:c,replace:f,state:h,target:p,to:g,preventScrollReset:v,viewTransition:w,...S},j){let{basename:O}=R.useContext(It),$=typeof g=="string"&&Cf.test(g),E,N=!1;if(typeof g=="string"&&$&&(E=g,xf))try{let je=new URL(window.location.href),ze=g.startsWith("//")?new URL(je.protocol+g):new URL(g),Ct=Jt(ze.pathname,O);ze.origin===je.origin&&Ct!=null?g=Ct+ze.search+ze.hash:N=!0}catch{$t(!1,`<Link to="${g}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let J=Pm(g,{relative:a}),[D,W,G]=lg(l,S),ee=gg(g,{replace:f,state:h,target:p,preventScrollReset:v,relative:a,viewTransition:w});function re(je){r&&r(je),je.defaultPrevented||ee(je)}let be=R.createElement("a",{...S,...G,href:E||J,onClick:N||c?r:re,ref:dg(j,W),target:p,"data-discover":!$&&i==="render"?"true":void 0});return D&&!$?R.createElement(R.Fragment,null,be,R.createElement(ag,{page:J})):be});tt.displayName="Link";var hg=R.forwardRef(function({"aria-current":r="page",caseSensitive:i=!1,className:l="",end:a=!1,style:c,to:f,viewTransition:h,children:p,...g},v){let w=Ei(f,{relative:g.relative}),S=yn(),j=R.useContext(Ks),{navigator:O,basename:$}=R.useContext(It),E=j!=null&&kg(w)&&h===!0,N=O.encodeLocation?O.encodeLocation(w).pathname:w.pathname,J=S.pathname,D=j&&j.navigation&&j.navigation.location?j.navigation.location.pathname:null;i||(J=J.toLowerCase(),D=D?D.toLowerCase():null,N=N.toLowerCase()),D&&$&&(D=Jt(D,$)||D);const W=N!=="/"&&N.endsWith("/")?N.length-1:N.length;let G=J===N||!a&&J.startsWith(N)&&J.charAt(W)==="/",ee=D!=null&&(D===N||!a&&D.startsWith(N)&&D.charAt(N.length)==="/"),re={isActive:G,isPending:ee,isTransitioning:E},be=G?r:void 0,je;typeof l=="function"?je=l(re):je=[l,G?"active":null,ee?"pending":null,E?"transitioning":null].filter(Boolean).join(" ");let ze=typeof c=="function"?c(re):c;return R.createElement(tt,{...g,"aria-current":be,className:je,ref:v,style:ze,to:f,viewTransition:h},typeof p=="function"?p(re):p)});hg.displayName="NavLink";var pg=R.forwardRef(({discover:o="render",fetcherKey:r,navigate:i,reloadDocument:l,replace:a,state:c,method:f=Bs,action:h,onSubmit:p,relative:g,preventScrollReset:v,viewTransition:w,...S},j)=>{let O=wg(),$=_g(h,{relative:g}),E=f.toLowerCase()==="get"?"get":"post",N=typeof h=="string"&&Cf.test(h),J=D=>{if(p&&p(D),D.defaultPrevented)return;D.preventDefault();let W=D.nativeEvent.submitter,G=W?.getAttribute("formmethod")||f;O(W||D.currentTarget,{fetcherKey:r,method:G,navigate:i,replace:a,state:c,relative:g,preventScrollReset:v,viewTransition:w})};return R.createElement("form",{ref:j,method:E,action:$,onSubmit:l?p:J,...S,"data-discover":!N&&o==="render"?"true":void 0})});pg.displayName="Form";function mg(o){return`${o} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Pf(o){let r=R.useContext(xr);return Se(r,mg(o)),r}function gg(o,{target:r,replace:i,state:l,preventScrollReset:a,relative:c,viewTransition:f}={}){let h=jm(),p=yn(),g=Ei(o,{relative:c});return R.useCallback(v=>{if(Jm(v,r)){v.preventDefault();let w=i!==void 0?i:yi(p)===yi(g);h(o,{replace:w,state:l,preventScrollReset:a,relative:c,viewTransition:f})}},[p,h,g,i,l,r,o,a,c,f])}var vg=0,yg=()=>`__${String(++vg)}__`;function wg(){let{router:o}=Pf("useSubmit"),{basename:r}=R.useContext(It),i=Um();return R.useCallback(async(l,a={})=>{let{action:c,method:f,encType:h,formData:p,body:g}=Ym(l,r);if(a.navigate===!1){let v=a.fetcherKey||yg();await o.fetch(v,i,a.action||c,{preventScrollReset:a.preventScrollReset,formData:p,body:g,formMethod:a.method||f,formEncType:a.encType||h,flushSync:a.flushSync})}else await o.navigate(a.action||c,{preventScrollReset:a.preventScrollReset,formData:p,body:g,formMethod:a.method||f,formEncType:a.encType||h,replace:a.replace,state:a.state,fromRouteId:i,flushSync:a.flushSync,viewTransition:a.viewTransition})},[o,r,i])}function _g(o,{relative:r}={}){let{basename:i}=R.useContext(It),l=R.useContext(At);Se(l,"useFormAction must be used inside a RouteContext");let[a]=l.matches.slice(-1),c={...Ei(o||".",{relative:r})},f=yn();if(o==null){c.search=f.search;let h=new URLSearchParams(c.search),p=h.getAll("index");if(p.some(v=>v==="")){h.delete("index"),p.filter(w=>w).forEach(w=>h.append("index",w));let v=h.toString();c.search=v?`?${v}`:""}}return(!o||o===".")&&a.route.index&&(c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index"),i!=="/"&&(c.pathname=c.pathname==="/"?i:Kt([i,c.pathname])),yi(c)}function kg(o,{relative:r}={}){let i=R.useContext(vf);Se(i!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:l}=Pf("useViewTransitionState"),a=Ei(o,{relative:r});if(!i.isTransitioning)return!1;let c=Jt(i.currentLocation.pathname,l)||i.currentLocation.pathname,f=Jt(i.nextLocation.pathname,l)||i.nextLocation.pathname;return Hs(a.pathname,f)!=null||Hs(a.pathname,c)!=null}const Sg=({onSearch:o,searchQuery:r,setSearchQuery:i})=>{const l=yn(),a=h=>!!(h==="/"&&l.pathname==="/"||h!=="/"&&l.pathname.startsWith(h)),c=h=>{const p=h.target.value;i(p),o&&o(p)},f=()=>l.pathname.startsWith("/authors")?"Search Authors...":"Search...";return L.jsxs("div",{className:"header",children:[L.jsx(tt,{to:"/",className:"logo",children:"SAYARI"}),L.jsxs("nav",{className:"nav",children:[L.jsx(tt,{to:"/",className:`nav-item ${a("/")?"active":""}`,children:"All"}),L.jsx(tt,{to:"/category/shayari",className:`nav-item ${a("/category/shayari")?"active":""}`,children:"Shayari"}),L.jsx(tt,{to:"/category/quotes",className:`nav-item ${a("/category/quotes")?"active":""}`,children:"Quotes"}),L.jsx(tt,{to:"/category/wishes",className:`nav-item ${a("/category/wishes")?"active":""}`,children:"Wishes"}),L.jsx(tt,{to:"/authors",className:`nav-item ${a("/authors")?"active":""}`,children:"Authors"})]}),L.jsx("div",{className:"search-container",children:L.jsx("input",{type:"text",className:"search",placeholder:f(),value:r,onChange:c})})]})},Eg=()=>L.jsx("div",{className:"footer",children:L.jsxs("div",{className:"footer-content",children:[L.jsx("p",{children:"© 2025 Sayari Blog. All rights reserved."}),L.jsx("p",{style:{marginTop:"10px",fontSize:"12px",color:"#999"},children:"A collection of beautiful Hindi Shayari, Quotes, and Wishes"})]})}),xg="modulepreload",Cg=function(o){return"/"+o},bd={},xi=function(r,i,l){let a=Promise.resolve();if(i&&i.length>0){let g=function(v){return Promise.all(v.map(w=>Promise.resolve(w).then(S=>({status:"fulfilled",value:S}),S=>({status:"rejected",reason:S}))))};var f=g;document.getElementsByTagName("link");const h=document.querySelector("meta[property=csp-nonce]"),p=h?.nonce||h?.getAttribute("nonce");a=g(i.map(v=>{if(v=Cg(v),v in bd)return;bd[v]=!0;const w=v.endsWith(".css"),S=w?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${v}"]${S}`))return;const j=document.createElement("link");if(j.rel=w?"stylesheet":xg,w||(j.as="script"),j.crossOrigin="",j.href=v,p&&j.setAttribute("nonce",p),document.head.appendChild(j),w)return new Promise((O,$)=>{j.addEventListener("load",O),j.addEventListener("error",()=>$(new Error(`Unable to preload CSS for ${v}`)))})}))}function c(h){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=h,window.dispatchEvent(p),!p.defaultPrevented)throw h}return a.then(h=>{for(const p of h||[])p.status==="rejected"&&c(p.reason);return r().catch(c)})},Pg=o=>{let r;return o?r=o:typeof fetch>"u"?r=(...i)=>xi(async()=>{const{default:l}=await Promise.resolve().then(()=>Cr);return{default:l}},[]).then(({default:l})=>l(...i)):r=fetch,(...i)=>r(...i)};class Ea extends Error{constructor(r,i="FunctionsError",l){super(r),this.name=i,this.context=l}}class jg extends Ea{constructor(r){super("Failed to send a request to the Edge Function","FunctionsFetchError",r)}}class Dd extends Ea{constructor(r){super("Relay Error invoking the Edge Function","FunctionsRelayError",r)}}class Ud extends Ea{constructor(r){super("Edge Function returned a non-2xx status code","FunctionsHttpError",r)}}var la;(function(o){o.Any="any",o.ApNortheast1="ap-northeast-1",o.ApNortheast2="ap-northeast-2",o.ApSouth1="ap-south-1",o.ApSoutheast1="ap-southeast-1",o.ApSoutheast2="ap-southeast-2",o.CaCentral1="ca-central-1",o.EuCentral1="eu-central-1",o.EuWest1="eu-west-1",o.EuWest2="eu-west-2",o.EuWest3="eu-west-3",o.SaEast1="sa-east-1",o.UsEast1="us-east-1",o.UsWest1="us-west-1",o.UsWest2="us-west-2"})(la||(la={}));var Tg=function(o,r,i,l){function a(c){return c instanceof i?c:new i(function(f){f(c)})}return new(i||(i=Promise))(function(c,f){function h(v){try{g(l.next(v))}catch(w){f(w)}}function p(v){try{g(l.throw(v))}catch(w){f(w)}}function g(v){v.done?c(v.value):a(v.value).then(h,p)}g((l=l.apply(o,r||[])).next())})};class Rg{constructor(r,{headers:i={},customFetch:l,region:a=la.Any}={}){this.url=r,this.headers=i,this.region=a,this.fetch=Pg(l)}setAuth(r){this.headers.Authorization=`Bearer ${r}`}invoke(r,i={}){var l;return Tg(this,void 0,void 0,function*(){try{const{headers:a,method:c,body:f}=i;let h={},{region:p}=i;p||(p=this.region);const g=new URL(`${this.url}/${r}`);p&&p!=="any"&&(h["x-region"]=p,g.searchParams.set("forceFunctionRegion",p));let v;f&&(a&&!Object.prototype.hasOwnProperty.call(a,"Content-Type")||!a)&&(typeof Blob<"u"&&f instanceof Blob||f instanceof ArrayBuffer?(h["Content-Type"]="application/octet-stream",v=f):typeof f=="string"?(h["Content-Type"]="text/plain",v=f):typeof FormData<"u"&&f instanceof FormData?v=f:(h["Content-Type"]="application/json",v=JSON.stringify(f)));const w=yield this.fetch(g.toString(),{method:c||"POST",headers:Object.assign(Object.assign(Object.assign({},h),this.headers),a),body:v}).catch($=>{throw new jg($)}),S=w.headers.get("x-relay-error");if(S&&S==="true")throw new Dd(w);if(!w.ok)throw new Ud(w);let j=((l=w.headers.get("Content-Type"))!==null&&l!==void 0?l:"text/plain").split(";")[0].trim(),O;return j==="application/json"?O=yield w.json():j==="application/octet-stream"?O=yield w.blob():j==="text/event-stream"?O=w:j==="multipart/form-data"?O=yield w.formData():O=yield w.text(),{data:O,error:null,response:w}}catch(a){return{data:null,error:a,response:a instanceof Ud||a instanceof Dd?a.context:void 0}}})}}var Ie={},cr={},dr={},fr={},hr={},pr={},Og=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},Sr=Og();const Ng=Sr.fetch,jf=Sr.fetch.bind(Sr),Tf=Sr.Headers,Lg=Sr.Request,$g=Sr.Response,Cr=Object.freeze(Object.defineProperty({__proto__:null,Headers:Tf,Request:Lg,Response:$g,default:jf,fetch:Ng},Symbol.toStringTag,{value:"Module"})),Ig=Wp(Cr);var Us={},zd;function Rf(){if(zd)return Us;zd=1,Object.defineProperty(Us,"__esModule",{value:!0});class o extends Error{constructor(i){super(i.message),this.name="PostgrestError",this.details=i.details,this.hint=i.hint,this.code=i.code}}return Us.default=o,Us}var Md;function Of(){if(Md)return pr;Md=1;var o=pr&&pr.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(pr,"__esModule",{value:!0});const r=o(Ig),i=o(Rf());class l{constructor(c){this.shouldThrowOnError=!1,this.method=c.method,this.url=c.url,this.headers=c.headers,this.schema=c.schema,this.body=c.body,this.shouldThrowOnError=c.shouldThrowOnError,this.signal=c.signal,this.isMaybeSingle=c.isMaybeSingle,c.fetch?this.fetch=c.fetch:typeof fetch>"u"?this.fetch=r.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(c,f){return this.headers=Object.assign({},this.headers),this.headers[c]=f,this}then(c,f){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const h=this.fetch;let p=h(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async g=>{var v,w,S;let j=null,O=null,$=null,E=g.status,N=g.statusText;if(g.ok){if(this.method!=="HEAD"){const G=await g.text();G===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?O=G:O=JSON.parse(G))}const D=(v=this.headers.Prefer)===null||v===void 0?void 0:v.match(/count=(exact|planned|estimated)/),W=(w=g.headers.get("content-range"))===null||w===void 0?void 0:w.split("/");D&&W&&W.length>1&&($=parseInt(W[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(O)&&(O.length>1?(j={code:"PGRST116",details:`Results contain ${O.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},O=null,$=null,E=406,N="Not Acceptable"):O.length===1?O=O[0]:O=null)}else{const D=await g.text();try{j=JSON.parse(D),Array.isArray(j)&&g.status===404&&(O=[],j=null,E=200,N="OK")}catch{g.status===404&&D===""?(E=204,N="No Content"):j={message:D}}if(j&&this.isMaybeSingle&&(!((S=j?.details)===null||S===void 0)&&S.includes("0 rows"))&&(j=null,E=200,N="OK"),j&&this.shouldThrowOnError)throw new i.default(j)}return{error:j,data:O,count:$,status:E,statusText:N}});return this.shouldThrowOnError||(p=p.catch(g=>{var v,w,S;return{error:{message:`${(v=g?.name)!==null&&v!==void 0?v:"FetchError"}: ${g?.message}`,details:`${(w=g?.stack)!==null&&w!==void 0?w:""}`,hint:"",code:`${(S=g?.code)!==null&&S!==void 0?S:""}`},data:null,count:null,status:0,statusText:""}})),p.then(c,f)}returns(){return this}overrideTypes(){return this}}return pr.default=l,pr}var Fd;function Nf(){if(Fd)return hr;Fd=1;var o=hr&&hr.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(hr,"__esModule",{value:!0});const r=o(Of());class i extends r.default{select(a){let c=!1;const f=(a??"*").split("").map(h=>/\s/.test(h)&&!c?"":(h==='"'&&(c=!c),h)).join("");return this.url.searchParams.set("select",f),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(a,{ascending:c=!0,nullsFirst:f,foreignTable:h,referencedTable:p=h}={}){const g=p?`${p}.order`:"order",v=this.url.searchParams.get(g);return this.url.searchParams.set(g,`${v?`${v},`:""}${a}.${c?"asc":"desc"}${f===void 0?"":f?".nullsfirst":".nullslast"}`),this}limit(a,{foreignTable:c,referencedTable:f=c}={}){const h=typeof f>"u"?"limit":`${f}.limit`;return this.url.searchParams.set(h,`${a}`),this}range(a,c,{foreignTable:f,referencedTable:h=f}={}){const p=typeof h>"u"?"offset":`${h}.offset`,g=typeof h>"u"?"limit":`${h}.limit`;return this.url.searchParams.set(p,`${a}`),this.url.searchParams.set(g,`${c-a+1}`),this}abortSignal(a){return this.signal=a,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:a=!1,verbose:c=!1,settings:f=!1,buffers:h=!1,wal:p=!1,format:g="text"}={}){var v;const w=[a?"analyze":null,c?"verbose":null,f?"settings":null,h?"buffers":null,p?"wal":null].filter(Boolean).join("|"),S=(v=this.headers.Accept)!==null&&v!==void 0?v:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${g}; for="${S}"; options=${w};`,g==="json"?this:this}rollback(){var a;return((a=this.headers.Prefer)!==null&&a!==void 0?a:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return hr.default=i,hr}var Bd;function xa(){if(Bd)return fr;Bd=1;var o=fr&&fr.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(fr,"__esModule",{value:!0});const r=o(Nf());class i extends r.default{eq(a,c){return this.url.searchParams.append(a,`eq.${c}`),this}neq(a,c){return this.url.searchParams.append(a,`neq.${c}`),this}gt(a,c){return this.url.searchParams.append(a,`gt.${c}`),this}gte(a,c){return this.url.searchParams.append(a,`gte.${c}`),this}lt(a,c){return this.url.searchParams.append(a,`lt.${c}`),this}lte(a,c){return this.url.searchParams.append(a,`lte.${c}`),this}like(a,c){return this.url.searchParams.append(a,`like.${c}`),this}likeAllOf(a,c){return this.url.searchParams.append(a,`like(all).{${c.join(",")}}`),this}likeAnyOf(a,c){return this.url.searchParams.append(a,`like(any).{${c.join(",")}}`),this}ilike(a,c){return this.url.searchParams.append(a,`ilike.${c}`),this}ilikeAllOf(a,c){return this.url.searchParams.append(a,`ilike(all).{${c.join(",")}}`),this}ilikeAnyOf(a,c){return this.url.searchParams.append(a,`ilike(any).{${c.join(",")}}`),this}is(a,c){return this.url.searchParams.append(a,`is.${c}`),this}in(a,c){const f=Array.from(new Set(c)).map(h=>typeof h=="string"&&new RegExp("[,()]").test(h)?`"${h}"`:`${h}`).join(",");return this.url.searchParams.append(a,`in.(${f})`),this}contains(a,c){return typeof c=="string"?this.url.searchParams.append(a,`cs.${c}`):Array.isArray(c)?this.url.searchParams.append(a,`cs.{${c.join(",")}}`):this.url.searchParams.append(a,`cs.${JSON.stringify(c)}`),this}containedBy(a,c){return typeof c=="string"?this.url.searchParams.append(a,`cd.${c}`):Array.isArray(c)?this.url.searchParams.append(a,`cd.{${c.join(",")}}`):this.url.searchParams.append(a,`cd.${JSON.stringify(c)}`),this}rangeGt(a,c){return this.url.searchParams.append(a,`sr.${c}`),this}rangeGte(a,c){return this.url.searchParams.append(a,`nxl.${c}`),this}rangeLt(a,c){return this.url.searchParams.append(a,`sl.${c}`),this}rangeLte(a,c){return this.url.searchParams.append(a,`nxr.${c}`),this}rangeAdjacent(a,c){return this.url.searchParams.append(a,`adj.${c}`),this}overlaps(a,c){return typeof c=="string"?this.url.searchParams.append(a,`ov.${c}`):this.url.searchParams.append(a,`ov.{${c.join(",")}}`),this}textSearch(a,c,{config:f,type:h}={}){let p="";h==="plain"?p="pl":h==="phrase"?p="ph":h==="websearch"&&(p="w");const g=f===void 0?"":`(${f})`;return this.url.searchParams.append(a,`${p}fts${g}.${c}`),this}match(a){return Object.entries(a).forEach(([c,f])=>{this.url.searchParams.append(c,`eq.${f}`)}),this}not(a,c,f){return this.url.searchParams.append(a,`not.${c}.${f}`),this}or(a,{foreignTable:c,referencedTable:f=c}={}){const h=f?`${f}.or`:"or";return this.url.searchParams.append(h,`(${a})`),this}filter(a,c,f){return this.url.searchParams.append(a,`${c}.${f}`),this}}return fr.default=i,fr}var Wd;function Lf(){if(Wd)return dr;Wd=1;var o=dr&&dr.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(dr,"__esModule",{value:!0});const r=o(xa());class i{constructor(a,{headers:c={},schema:f,fetch:h}){this.url=a,this.headers=c,this.schema=f,this.fetch=h}select(a,{head:c=!1,count:f}={}){const h=c?"HEAD":"GET";let p=!1;const g=(a??"*").split("").map(v=>/\s/.test(v)&&!p?"":(v==='"'&&(p=!p),v)).join("");return this.url.searchParams.set("select",g),f&&(this.headers.Prefer=`count=${f}`),new r.default({method:h,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(a,{count:c,defaultToNull:f=!0}={}){const h="POST",p=[];if(this.headers.Prefer&&p.push(this.headers.Prefer),c&&p.push(`count=${c}`),f||p.push("missing=default"),this.headers.Prefer=p.join(","),Array.isArray(a)){const g=a.reduce((v,w)=>v.concat(Object.keys(w)),[]);if(g.length>0){const v=[...new Set(g)].map(w=>`"${w}"`);this.url.searchParams.set("columns",v.join(","))}}return new r.default({method:h,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}upsert(a,{onConflict:c,ignoreDuplicates:f=!1,count:h,defaultToNull:p=!0}={}){const g="POST",v=[`resolution=${f?"ignore":"merge"}-duplicates`];if(c!==void 0&&this.url.searchParams.set("on_conflict",c),this.headers.Prefer&&v.push(this.headers.Prefer),h&&v.push(`count=${h}`),p||v.push("missing=default"),this.headers.Prefer=v.join(","),Array.isArray(a)){const w=a.reduce((S,j)=>S.concat(Object.keys(j)),[]);if(w.length>0){const S=[...new Set(w)].map(j=>`"${j}"`);this.url.searchParams.set("columns",S.join(","))}}return new r.default({method:g,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}update(a,{count:c}={}){const f="PATCH",h=[];return this.headers.Prefer&&h.push(this.headers.Prefer),c&&h.push(`count=${c}`),this.headers.Prefer=h.join(","),new r.default({method:f,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}delete({count:a}={}){const c="DELETE",f=[];return a&&f.push(`count=${a}`),this.headers.Prefer&&f.unshift(this.headers.Prefer),this.headers.Prefer=f.join(","),new r.default({method:c,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return dr.default=i,dr}var hi={},pi={},Hd;function Ag(){return Hd||(Hd=1,Object.defineProperty(pi,"__esModule",{value:!0}),pi.version=void 0,pi.version="0.0.0-automated"),pi}var qd;function bg(){if(qd)return hi;qd=1,Object.defineProperty(hi,"__esModule",{value:!0}),hi.DEFAULT_HEADERS=void 0;const o=Ag();return hi.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${o.version}`},hi}var Vd;function Dg(){if(Vd)return cr;Vd=1;var o=cr&&cr.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(cr,"__esModule",{value:!0});const r=o(Lf()),i=o(xa()),l=bg();class a{constructor(f,{headers:h={},schema:p,fetch:g}={}){this.url=f,this.headers=Object.assign(Object.assign({},l.DEFAULT_HEADERS),h),this.schemaName=p,this.fetch=g}from(f){const h=new URL(`${this.url}/${f}`);return new r.default(h,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(f){return new a(this.url,{headers:this.headers,schema:f,fetch:this.fetch})}rpc(f,h={},{head:p=!1,get:g=!1,count:v}={}){let w;const S=new URL(`${this.url}/rpc/${f}`);let j;p||g?(w=p?"HEAD":"GET",Object.entries(h).filter(([$,E])=>E!==void 0).map(([$,E])=>[$,Array.isArray(E)?`{${E.join(",")}}`:`${E}`]).forEach(([$,E])=>{S.searchParams.append($,E)})):(w="POST",j=h);const O=Object.assign({},this.headers);return v&&(O.Prefer=`count=${v}`),new i.default({method:w,url:S,headers:O,schema:this.schemaName,body:j,fetch:this.fetch,allowEmpty:!1})}}return cr.default=a,cr}var Kd;function Ug(){if(Kd)return Ie;Kd=1;var o=Ie&&Ie.__importDefault||function(h){return h&&h.__esModule?h:{default:h}};Object.defineProperty(Ie,"__esModule",{value:!0}),Ie.PostgrestError=Ie.PostgrestBuilder=Ie.PostgrestTransformBuilder=Ie.PostgrestFilterBuilder=Ie.PostgrestQueryBuilder=Ie.PostgrestClient=void 0;const r=o(Dg());Ie.PostgrestClient=r.default;const i=o(Lf());Ie.PostgrestQueryBuilder=i.default;const l=o(xa());Ie.PostgrestFilterBuilder=l.default;const a=o(Nf());Ie.PostgrestTransformBuilder=a.default;const c=o(Of());Ie.PostgrestBuilder=c.default;const f=o(Rf());return Ie.PostgrestError=f.default,Ie.default={PostgrestClient:r.default,PostgrestQueryBuilder:i.default,PostgrestFilterBuilder:l.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:c.default,PostgrestError:f.default},Ie}var zg=Ug();const Mg=Bp(zg),{PostgrestClient:Fg,PostgrestQueryBuilder:Ay,PostgrestFilterBuilder:by,PostgrestTransformBuilder:Dy,PostgrestBuilder:Uy,PostgrestError:zy}=Mg;function Bg(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const Wg=Bg(),Hg="2.11.15",qg=`realtime-js/${Hg}`,Vg="1.0.0",$f=1e4,Kg=1e3;var gi;(function(o){o[o.connecting=0]="connecting",o[o.open=1]="open",o[o.closing=2]="closing",o[o.closed=3]="closed"})(gi||(gi={}));var He;(function(o){o.closed="closed",o.errored="errored",o.joined="joined",o.joining="joining",o.leaving="leaving"})(He||(He={}));var xt;(function(o){o.close="phx_close",o.error="phx_error",o.join="phx_join",o.reply="phx_reply",o.leave="phx_leave",o.access_token="access_token"})(xt||(xt={}));var aa;(function(o){o.websocket="websocket"})(aa||(aa={}));var Dn;(function(o){o.Connecting="connecting",o.Open="open",o.Closing="closing",o.Closed="closed"})(Dn||(Dn={}));class Jg{constructor(){this.HEADER_LENGTH=1}decode(r,i){return r.constructor===ArrayBuffer?i(this._binaryDecode(r)):i(typeof r=="string"?JSON.parse(r):{})}_binaryDecode(r){const i=new DataView(r),l=new TextDecoder;return this._decodeBroadcast(r,i,l)}_decodeBroadcast(r,i,l){const a=i.getUint8(1),c=i.getUint8(2);let f=this.HEADER_LENGTH+2;const h=l.decode(r.slice(f,f+a));f=f+a;const p=l.decode(r.slice(f,f+c));f=f+c;const g=JSON.parse(l.decode(r.slice(f,r.byteLength)));return{ref:null,topic:h,event:p,payload:g}}}class If{constructor(r,i){this.callback=r,this.timerCalc=i,this.timer=void 0,this.tries=0,this.callback=r,this.timerCalc=i}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var me;(function(o){o.abstime="abstime",o.bool="bool",o.date="date",o.daterange="daterange",o.float4="float4",o.float8="float8",o.int2="int2",o.int4="int4",o.int4range="int4range",o.int8="int8",o.int8range="int8range",o.json="json",o.jsonb="jsonb",o.money="money",o.numeric="numeric",o.oid="oid",o.reltime="reltime",o.text="text",o.time="time",o.timestamp="timestamp",o.timestamptz="timestamptz",o.timetz="timetz",o.tsrange="tsrange",o.tstzrange="tstzrange"})(me||(me={}));const Jd=(o,r,i={})=>{var l;const a=(l=i.skipTypes)!==null&&l!==void 0?l:[];return Object.keys(r).reduce((c,f)=>(c[f]=Gg(f,o,r,a),c),{})},Gg=(o,r,i,l)=>{const a=r.find(h=>h.name===o),c=a?.type,f=i[o];return c&&!l.includes(c)?Af(c,f):ua(f)},Af=(o,r)=>{if(o.charAt(0)==="_"){const i=o.slice(1,o.length);return Zg(r,i)}switch(o){case me.bool:return Qg(r);case me.float4:case me.float8:case me.int2:case me.int4:case me.int8:case me.numeric:case me.oid:return Yg(r);case me.json:case me.jsonb:return Xg(r);case me.timestamp:return ev(r);case me.abstime:case me.date:case me.daterange:case me.int4range:case me.int8range:case me.money:case me.reltime:case me.text:case me.time:case me.timestamptz:case me.timetz:case me.tsrange:case me.tstzrange:return ua(r);default:return ua(r)}},ua=o=>o,Qg=o=>{switch(o){case"t":return!0;case"f":return!1;default:return o}},Yg=o=>{if(typeof o=="string"){const r=parseFloat(o);if(!Number.isNaN(r))return r}return o},Xg=o=>{if(typeof o=="string")try{return JSON.parse(o)}catch(r){return console.log(`JSON parse error: ${r}`),o}return o},Zg=(o,r)=>{if(typeof o!="string")return o;const i=o.length-1,l=o[i];if(o[0]==="{"&&l==="}"){let c;const f=o.slice(1,i);try{c=JSON.parse("["+f+"]")}catch{c=f?f.split(","):[]}return c.map(h=>Af(r,h))}return o},ev=o=>typeof o=="string"?o.replace(" ","T"):o,bf=o=>{let r=o;return r=r.replace(/^ws/i,"http"),r=r.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),r.replace(/\/+$/,"")};class Xl{constructor(r,i,l={},a=$f){this.channel=r,this.event=i,this.payload=l,this.timeout=a,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(r){this.timeout=r,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(r){this.payload=Object.assign(Object.assign({},this.payload),r)}receive(r,i){var l;return this._hasReceived(r)&&i((l=this.receivedResp)===null||l===void 0?void 0:l.response),this.recHooks.push({status:r,callback:i}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const r=i=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=i,this._matchReceive(i)};this.channel._on(this.refEvent,{},r),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(r,i){this.refEvent&&this.channel._trigger(this.refEvent,{status:r,response:i})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:r,response:i}){this.recHooks.filter(l=>l.status===r).forEach(l=>l.callback(i))}_hasReceived(r){return this.receivedResp&&this.receivedResp.status===r}}var Gd;(function(o){o.SYNC="sync",o.JOIN="join",o.LEAVE="leave"})(Gd||(Gd={}));class vi{constructor(r,i){this.channel=r,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const l=i?.events||{state:"presence_state",diff:"presence_diff"};this.channel._on(l.state,{},a=>{const{onJoin:c,onLeave:f,onSync:h}=this.caller;this.joinRef=this.channel._joinRef(),this.state=vi.syncState(this.state,a,c,f),this.pendingDiffs.forEach(p=>{this.state=vi.syncDiff(this.state,p,c,f)}),this.pendingDiffs=[],h()}),this.channel._on(l.diff,{},a=>{const{onJoin:c,onLeave:f,onSync:h}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(a):(this.state=vi.syncDiff(this.state,a,c,f),h())}),this.onJoin((a,c,f)=>{this.channel._trigger("presence",{event:"join",key:a,currentPresences:c,newPresences:f})}),this.onLeave((a,c,f)=>{this.channel._trigger("presence",{event:"leave",key:a,currentPresences:c,leftPresences:f})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(r,i,l,a){const c=this.cloneDeep(r),f=this.transformState(i),h={},p={};return this.map(c,(g,v)=>{f[g]||(p[g]=v)}),this.map(f,(g,v)=>{const w=c[g];if(w){const S=v.map(E=>E.presence_ref),j=w.map(E=>E.presence_ref),O=v.filter(E=>j.indexOf(E.presence_ref)<0),$=w.filter(E=>S.indexOf(E.presence_ref)<0);O.length>0&&(h[g]=O),$.length>0&&(p[g]=$)}else h[g]=v}),this.syncDiff(c,{joins:h,leaves:p},l,a)}static syncDiff(r,i,l,a){const{joins:c,leaves:f}={joins:this.transformState(i.joins),leaves:this.transformState(i.leaves)};return l||(l=()=>{}),a||(a=()=>{}),this.map(c,(h,p)=>{var g;const v=(g=r[h])!==null&&g!==void 0?g:[];if(r[h]=this.cloneDeep(p),v.length>0){const w=r[h].map(j=>j.presence_ref),S=v.filter(j=>w.indexOf(j.presence_ref)<0);r[h].unshift(...S)}l(h,v,p)}),this.map(f,(h,p)=>{let g=r[h];if(!g)return;const v=p.map(w=>w.presence_ref);g=g.filter(w=>v.indexOf(w.presence_ref)<0),r[h]=g,a(h,g,p),g.length===0&&delete r[h]}),r}static map(r,i){return Object.getOwnPropertyNames(r).map(l=>i(l,r[l]))}static transformState(r){return r=this.cloneDeep(r),Object.getOwnPropertyNames(r).reduce((i,l)=>{const a=r[l];return"metas"in a?i[l]=a.metas.map(c=>(c.presence_ref=c.phx_ref,delete c.phx_ref,delete c.phx_ref_prev,c)):i[l]=a,i},{})}static cloneDeep(r){return JSON.parse(JSON.stringify(r))}onJoin(r){this.caller.onJoin=r}onLeave(r){this.caller.onLeave=r}onSync(r){this.caller.onSync=r}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Qd;(function(o){o.ALL="*",o.INSERT="INSERT",o.UPDATE="UPDATE",o.DELETE="DELETE"})(Qd||(Qd={}));var Yd;(function(o){o.BROADCAST="broadcast",o.PRESENCE="presence",o.POSTGRES_CHANGES="postgres_changes",o.SYSTEM="system"})(Yd||(Yd={}));var qt;(function(o){o.SUBSCRIBED="SUBSCRIBED",o.TIMED_OUT="TIMED_OUT",o.CLOSED="CLOSED",o.CHANNEL_ERROR="CHANNEL_ERROR"})(qt||(qt={}));class Ca{constructor(r,i={config:{}},l){this.topic=r,this.params=i,this.socket=l,this.bindings={},this.state=He.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=r.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},i.config),this.timeout=this.socket.timeout,this.joinPush=new Xl(this,xt.join,this.params,this.timeout),this.rejoinTimer=new If(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=He.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(a=>a.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=He.closed,this.socket._remove(this)}),this._onError(a=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,a),this.state=He.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=He.errored,this.rejoinTimer.scheduleTimeout())}),this._on(xt.reply,{},(a,c)=>{this._trigger(this._replyEventName(c),a)}),this.presence=new vi(this),this.broadcastEndpointURL=bf(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(r,i=this.timeout){var l,a;if(this.socket.isConnected()||this.socket.connect(),this.state==He.closed){const{config:{broadcast:c,presence:f,private:h}}=this.params;this._onError(v=>r?.(qt.CHANNEL_ERROR,v)),this._onClose(()=>r?.(qt.CLOSED));const p={},g={broadcast:c,presence:f,postgres_changes:(a=(l=this.bindings.postgres_changes)===null||l===void 0?void 0:l.map(v=>v.filter))!==null&&a!==void 0?a:[],private:h};this.socket.accessTokenValue&&(p.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:g},p)),this.joinedOnce=!0,this._rejoin(i),this.joinPush.receive("ok",async({postgres_changes:v})=>{var w;if(this.socket.setAuth(),v===void 0){r?.(qt.SUBSCRIBED);return}else{const S=this.bindings.postgres_changes,j=(w=S?.length)!==null&&w!==void 0?w:0,O=[];for(let $=0;$<j;$++){const E=S[$],{filter:{event:N,schema:J,table:D,filter:W}}=E,G=v&&v[$];if(G&&G.event===N&&G.schema===J&&G.table===D&&G.filter===W)O.push(Object.assign(Object.assign({},E),{id:G.id}));else{this.unsubscribe(),this.state=He.errored,r?.(qt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=O,r&&r(qt.SUBSCRIBED);return}}).receive("error",v=>{this.state=He.errored,r?.(qt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(v).join(", ")||"error")))}).receive("timeout",()=>{r?.(qt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(r,i={}){return await this.send({type:"presence",event:"track",payload:r},i.timeout||this.timeout)}async untrack(r={}){return await this.send({type:"presence",event:"untrack"},r)}on(r,i,l){return this._on(r,i,l)}async send(r,i={}){var l,a;if(!this._canPush()&&r.type==="broadcast"){const{event:c,payload:f}=r,p={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:c,payload:f,private:this.private}]})};try{const g=await this._fetchWithTimeout(this.broadcastEndpointURL,p,(l=i.timeout)!==null&&l!==void 0?l:this.timeout);return await((a=g.body)===null||a===void 0?void 0:a.cancel()),g.ok?"ok":"error"}catch(g){return g.name==="AbortError"?"timed out":"error"}}else return new Promise(c=>{var f,h,p;const g=this._push(r.type,r,i.timeout||this.timeout);r.type==="broadcast"&&!(!((p=(h=(f=this.params)===null||f===void 0?void 0:f.config)===null||h===void 0?void 0:h.broadcast)===null||p===void 0)&&p.ack)&&c("ok"),g.receive("ok",()=>c("ok")),g.receive("error",()=>c("error")),g.receive("timeout",()=>c("timed out"))})}updateJoinPayload(r){this.joinPush.updatePayload(r)}unsubscribe(r=this.timeout){this.state=He.leaving;const i=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(xt.close,"leave",this._joinRef())};this.joinPush.destroy();let l=null;return new Promise(a=>{l=new Xl(this,xt.leave,{},r),l.receive("ok",()=>{i(),a("ok")}).receive("timeout",()=>{i(),a("timed out")}).receive("error",()=>{a("error")}),l.send(),this._canPush()||l.trigger("ok",{})}).finally(()=>{l?.destroy()})}teardown(){this.pushBuffer.forEach(r=>r.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(r,i,l){const a=new AbortController,c=setTimeout(()=>a.abort(),l),f=await this.socket.fetch(r,Object.assign(Object.assign({},i),{signal:a.signal}));return clearTimeout(c),f}_push(r,i,l=this.timeout){if(!this.joinedOnce)throw`tried to push '${r}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let a=new Xl(this,r,i,l);return this._canPush()?a.send():(a.startTimeout(),this.pushBuffer.push(a)),a}_onMessage(r,i,l){return i}_isMember(r){return this.topic===r}_joinRef(){return this.joinPush.ref}_trigger(r,i,l){var a,c;const f=r.toLocaleLowerCase(),{close:h,error:p,leave:g,join:v}=xt;if(l&&[h,p,g,v].indexOf(f)>=0&&l!==this._joinRef())return;let S=this._onMessage(f,i,l);if(i&&!S)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(f)?(a=this.bindings.postgres_changes)===null||a===void 0||a.filter(j=>{var O,$,E;return((O=j.filter)===null||O===void 0?void 0:O.event)==="*"||((E=($=j.filter)===null||$===void 0?void 0:$.event)===null||E===void 0?void 0:E.toLocaleLowerCase())===f}).map(j=>j.callback(S,l)):(c=this.bindings[f])===null||c===void 0||c.filter(j=>{var O,$,E,N,J,D;if(["broadcast","presence","postgres_changes"].includes(f))if("id"in j){const W=j.id,G=(O=j.filter)===null||O===void 0?void 0:O.event;return W&&(($=i.ids)===null||$===void 0?void 0:$.includes(W))&&(G==="*"||G?.toLocaleLowerCase()===((E=i.data)===null||E===void 0?void 0:E.type.toLocaleLowerCase()))}else{const W=(J=(N=j?.filter)===null||N===void 0?void 0:N.event)===null||J===void 0?void 0:J.toLocaleLowerCase();return W==="*"||W===((D=i?.event)===null||D===void 0?void 0:D.toLocaleLowerCase())}else return j.type.toLocaleLowerCase()===f}).map(j=>{if(typeof S=="object"&&"ids"in S){const O=S.data,{schema:$,table:E,commit_timestamp:N,type:J,errors:D}=O;S=Object.assign(Object.assign({},{schema:$,table:E,commit_timestamp:N,eventType:J,new:{},old:{},errors:D}),this._getPayloadRecords(O))}j.callback(S,l)})}_isClosed(){return this.state===He.closed}_isJoined(){return this.state===He.joined}_isJoining(){return this.state===He.joining}_isLeaving(){return this.state===He.leaving}_replyEventName(r){return`chan_reply_${r}`}_on(r,i,l){const a=r.toLocaleLowerCase(),c={type:a,filter:i,callback:l};return this.bindings[a]?this.bindings[a].push(c):this.bindings[a]=[c],this}_off(r,i){const l=r.toLocaleLowerCase();return this.bindings[l]=this.bindings[l].filter(a=>{var c;return!(((c=a.type)===null||c===void 0?void 0:c.toLocaleLowerCase())===l&&Ca.isEqual(a.filter,i))}),this}static isEqual(r,i){if(Object.keys(r).length!==Object.keys(i).length)return!1;for(const l in r)if(r[l]!==i[l])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(r){this._on(xt.close,{},r)}_onError(r){this._on(xt.error,{},i=>r(i))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(r=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=He.joining,this.joinPush.resend(r))}_getPayloadRecords(r){const i={new:{},old:{}};return(r.type==="INSERT"||r.type==="UPDATE")&&(i.new=Jd(r.columns,r.record)),(r.type==="UPDATE"||r.type==="DELETE")&&(i.old=Jd(r.columns,r.old_record)),i}}const Xd=()=>{},tv=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class nv{constructor(r,i){var l;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=$f,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Xd,this.ref=0,this.logger=Xd,this.conn=null,this.sendBuffer=[],this.serializer=new Jg,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=c=>{let f;return c?f=c:typeof fetch>"u"?f=(...h)=>xi(async()=>{const{default:p}=await Promise.resolve().then(()=>Cr);return{default:p}},void 0).then(({default:p})=>p(...h)):f=fetch,(...h)=>f(...h)},this.endPoint=`${r}/${aa.websocket}`,this.httpEndpoint=bf(r),i?.transport?this.transport=i.transport:this.transport=null,i?.params&&(this.params=i.params),i?.timeout&&(this.timeout=i.timeout),i?.logger&&(this.logger=i.logger),(i?.logLevel||i?.log_level)&&(this.logLevel=i.logLevel||i.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),i?.heartbeatIntervalMs&&(this.heartbeatIntervalMs=i.heartbeatIntervalMs);const a=(l=i?.params)===null||l===void 0?void 0:l.apikey;if(a&&(this.accessTokenValue=a,this.apiKey=a),this.reconnectAfterMs=i?.reconnectAfterMs?i.reconnectAfterMs:c=>[1e3,2e3,5e3,1e4][c-1]||1e4,this.encode=i?.encode?i.encode:(c,f)=>f(JSON.stringify(c)),this.decode=i?.decode?i.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new If(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(i?.fetch),i?.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=i?.worker||!1,this.workerUrl=i?.workerUrl}this.accessToken=i?.accessToken||null}connect(){if(!this.conn){if(this.transport||(this.transport=Wg),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Vg}))}disconnect(r,i){this.conn&&(this.conn.onclose=function(){},r?this.conn.close(r,i??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(l=>l.teardown()))}getChannels(){return this.channels}async removeChannel(r){const i=await r.unsubscribe();return this.channels.length===0&&this.disconnect(),i}async removeAllChannels(){const r=await Promise.all(this.channels.map(i=>i.unsubscribe()));return this.channels=[],this.disconnect(),r}log(r,i,l){this.logger(r,i,l)}connectionState(){switch(this.conn&&this.conn.readyState){case gi.connecting:return Dn.Connecting;case gi.open:return Dn.Open;case gi.closing:return Dn.Closing;default:return Dn.Closed}}isConnected(){return this.connectionState()===Dn.Open}channel(r,i={config:{}}){const l=`realtime:${r}`,a=this.getChannels().find(c=>c.topic===l);if(a)return a;{const c=new Ca(`realtime:${r}`,i,this);return this.channels.push(c),c}}push(r){const{topic:i,event:l,payload:a,ref:c}=r,f=()=>{this.encode(r,h=>{var p;(p=this.conn)===null||p===void 0||p.send(h)})};this.log("push",`${i} ${l} (${c})`,a),this.isConnected()?f():this.sendBuffer.push(f)}async setAuth(r=null){let i=r||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=i&&(this.accessTokenValue=i,this.channels.forEach(l=>{const a={access_token:i,version:qg};i&&l.updateJoinPayload(a),l.joinedOnce&&l._isJoined()&&l._push(xt.access_token,{access_token:i})}))}async sendHeartbeat(){var r;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(r=this.conn)===null||r===void 0||r.close(Kg,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(r){this.heartbeatCallback=r}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(r=>r()),this.sendBuffer=[])}_makeRef(){let r=this.ref+1;return r===this.ref?this.ref=0:this.ref=r,this.ref.toString()}_leaveOpenTopic(r){let i=this.channels.find(l=>l.topic===r&&(l._isJoined()||l._isJoining()));i&&(this.log("transport",`leaving duplicate topic "${r}"`),i.unsubscribe())}_remove(r){this.channels=this.channels.filter(i=>i.topic!==r.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=r=>this._onConnError(r),this.conn.onmessage=r=>this._onConnMessage(r),this.conn.onclose=r=>this._onConnClose(r))}_onConnMessage(r){this.decode(r.data,i=>{let{topic:l,event:a,payload:c,ref:f}=i;l==="phoenix"&&a==="phx_reply"&&this.heartbeatCallback(i.payload.status=="ok"?"ok":"error"),f&&f===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${c.status||""} ${l} ${a} ${f&&"("+f+")"||""}`,c),Array.from(this.channels).filter(h=>h._isMember(l)).forEach(h=>h._trigger(a,c,f)),this.stateChangeCallbacks.message.forEach(h=>h(i))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(r=>r())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const r=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(r),this.workerRef.onerror=i=>{this.log("worker","worker error",i.message),this.workerRef.terminate()},this.workerRef.onmessage=i=>{i.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(r){this.log("transport","close",r),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(i=>i(r))}_onConnError(r){this.log("transport",`${r}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(i=>i(r))}_triggerChanError(){this.channels.forEach(r=>r._trigger(xt.error))}_appendParams(r,i){if(Object.keys(i).length===0)return r;const l=r.match(/\?/)?"&":"?",a=new URLSearchParams(i);return`${r}${l}${a}`}_workerObjectUrl(r){let i;if(r)i=r;else{const l=new Blob([tv],{type:"application/javascript"});i=URL.createObjectURL(l)}return i}}class Pa extends Error{constructor(r){super(r),this.__isStorageError=!0,this.name="StorageError"}}function Ae(o){return typeof o=="object"&&o!==null&&"__isStorageError"in o}class rv extends Pa{constructor(r,i,l){super(r),this.name="StorageApiError",this.status=i,this.statusCode=l}toJSON(){return{name:this.name,message:this.message,status:this.status,statusCode:this.statusCode}}}class ca extends Pa{constructor(r,i){super(r),this.name="StorageUnknownError",this.originalError=i}}var iv=function(o,r,i,l){function a(c){return c instanceof i?c:new i(function(f){f(c)})}return new(i||(i=Promise))(function(c,f){function h(v){try{g(l.next(v))}catch(w){f(w)}}function p(v){try{g(l.throw(v))}catch(w){f(w)}}function g(v){v.done?c(v.value):a(v.value).then(h,p)}g((l=l.apply(o,r||[])).next())})};const Df=o=>{let r;return o?r=o:typeof fetch>"u"?r=(...i)=>xi(async()=>{const{default:l}=await Promise.resolve().then(()=>Cr);return{default:l}},void 0).then(({default:l})=>l(...i)):r=fetch,(...i)=>r(...i)},sv=()=>iv(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield xi(()=>Promise.resolve().then(()=>Cr),void 0)).Response:Response}),da=o=>{if(Array.isArray(o))return o.map(i=>da(i));if(typeof o=="function"||o!==Object(o))return o;const r={};return Object.entries(o).forEach(([i,l])=>{const a=i.replace(/([-_][a-z])/gi,c=>c.toUpperCase().replace(/[-_]/g,""));r[a]=da(l)}),r},ov=o=>{if(typeof o!="object"||o===null)return!1;const r=Object.getPrototypeOf(o);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in o)&&!(Symbol.iterator in o)};var Un=function(o,r,i,l){function a(c){return c instanceof i?c:new i(function(f){f(c)})}return new(i||(i=Promise))(function(c,f){function h(v){try{g(l.next(v))}catch(w){f(w)}}function p(v){try{g(l.throw(v))}catch(w){f(w)}}function g(v){v.done?c(v.value):a(v.value).then(h,p)}g((l=l.apply(o,r||[])).next())})};const Zl=o=>o.msg||o.message||o.error_description||o.error||JSON.stringify(o),lv=(o,r,i)=>Un(void 0,void 0,void 0,function*(){const l=yield sv();o instanceof l&&!i?.noResolveJson?o.json().then(a=>{const c=o.status||500,f=a?.statusCode||c+"";r(new rv(Zl(a),c,f))}).catch(a=>{r(new ca(Zl(a),a))}):r(new ca(Zl(o),o))}),av=(o,r,i,l)=>{const a={method:o,headers:r?.headers||{}};return o==="GET"||!l?a:(ov(l)?(a.headers=Object.assign({"Content-Type":"application/json"},r?.headers),a.body=JSON.stringify(l)):a.body=l,Object.assign(Object.assign({},a),i))};function Ci(o,r,i,l,a,c){return Un(this,void 0,void 0,function*(){return new Promise((f,h)=>{o(i,av(r,l,a,c)).then(p=>{if(!p.ok)throw p;return l?.noResolveJson?p:p.json()}).then(p=>f(p)).catch(p=>lv(p,h,l))})})}function qs(o,r,i,l){return Un(this,void 0,void 0,function*(){return Ci(o,"GET",r,i,l)})}function Vt(o,r,i,l,a){return Un(this,void 0,void 0,function*(){return Ci(o,"POST",r,l,a,i)})}function fa(o,r,i,l,a){return Un(this,void 0,void 0,function*(){return Ci(o,"PUT",r,l,a,i)})}function uv(o,r,i,l){return Un(this,void 0,void 0,function*(){return Ci(o,"HEAD",r,Object.assign(Object.assign({},i),{noResolveJson:!0}),l)})}function Uf(o,r,i,l,a){return Un(this,void 0,void 0,function*(){return Ci(o,"DELETE",r,l,a,i)})}var et=function(o,r,i,l){function a(c){return c instanceof i?c:new i(function(f){f(c)})}return new(i||(i=Promise))(function(c,f){function h(v){try{g(l.next(v))}catch(w){f(w)}}function p(v){try{g(l.throw(v))}catch(w){f(w)}}function g(v){v.done?c(v.value):a(v.value).then(h,p)}g((l=l.apply(o,r||[])).next())})};const cv={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Zd={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class dv{constructor(r,i={},l,a){this.url=r,this.headers=i,this.bucketId=l,this.fetch=Df(a)}uploadOrUpdate(r,i,l,a){return et(this,void 0,void 0,function*(){try{let c;const f=Object.assign(Object.assign({},Zd),a);let h=Object.assign(Object.assign({},this.headers),r==="POST"&&{"x-upsert":String(f.upsert)});const p=f.metadata;typeof Blob<"u"&&l instanceof Blob?(c=new FormData,c.append("cacheControl",f.cacheControl),p&&c.append("metadata",this.encodeMetadata(p)),c.append("",l)):typeof FormData<"u"&&l instanceof FormData?(c=l,c.append("cacheControl",f.cacheControl),p&&c.append("metadata",this.encodeMetadata(p))):(c=l,h["cache-control"]=`max-age=${f.cacheControl}`,h["content-type"]=f.contentType,p&&(h["x-metadata"]=this.toBase64(this.encodeMetadata(p)))),a?.headers&&(h=Object.assign(Object.assign({},h),a.headers));const g=this._removeEmptyFolders(i),v=this._getFinalPath(g),w=yield(r=="PUT"?fa:Vt)(this.fetch,`${this.url}/object/${v}`,c,Object.assign({headers:h},f?.duplex?{duplex:f.duplex}:{}));return{data:{path:g,id:w.Id,fullPath:w.Key},error:null}}catch(c){if(Ae(c))return{data:null,error:c};throw c}})}upload(r,i,l){return et(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",r,i,l)})}uploadToSignedUrl(r,i,l,a){return et(this,void 0,void 0,function*(){const c=this._removeEmptyFolders(r),f=this._getFinalPath(c),h=new URL(this.url+`/object/upload/sign/${f}`);h.searchParams.set("token",i);try{let p;const g=Object.assign({upsert:Zd.upsert},a),v=Object.assign(Object.assign({},this.headers),{"x-upsert":String(g.upsert)});typeof Blob<"u"&&l instanceof Blob?(p=new FormData,p.append("cacheControl",g.cacheControl),p.append("",l)):typeof FormData<"u"&&l instanceof FormData?(p=l,p.append("cacheControl",g.cacheControl)):(p=l,v["cache-control"]=`max-age=${g.cacheControl}`,v["content-type"]=g.contentType);const w=yield fa(this.fetch,h.toString(),p,{headers:v});return{data:{path:c,fullPath:w.Key},error:null}}catch(p){if(Ae(p))return{data:null,error:p};throw p}})}createSignedUploadUrl(r,i){return et(this,void 0,void 0,function*(){try{let l=this._getFinalPath(r);const a=Object.assign({},this.headers);i?.upsert&&(a["x-upsert"]="true");const c=yield Vt(this.fetch,`${this.url}/object/upload/sign/${l}`,{},{headers:a}),f=new URL(this.url+c.url),h=f.searchParams.get("token");if(!h)throw new Pa("No token returned by API");return{data:{signedUrl:f.toString(),path:r,token:h},error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}update(r,i,l){return et(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",r,i,l)})}move(r,i,l){return et(this,void 0,void 0,function*(){try{return{data:yield Vt(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:r,destinationKey:i,destinationBucket:l?.destinationBucket},{headers:this.headers}),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}copy(r,i,l){return et(this,void 0,void 0,function*(){try{return{data:{path:(yield Vt(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:r,destinationKey:i,destinationBucket:l?.destinationBucket},{headers:this.headers})).Key},error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}createSignedUrl(r,i,l){return et(this,void 0,void 0,function*(){try{let a=this._getFinalPath(r),c=yield Vt(this.fetch,`${this.url}/object/sign/${a}`,Object.assign({expiresIn:i},l?.transform?{transform:l.transform}:{}),{headers:this.headers});const f=l?.download?`&download=${l.download===!0?"":l.download}`:"";return c={signedUrl:encodeURI(`${this.url}${c.signedURL}${f}`)},{data:c,error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}createSignedUrls(r,i,l){return et(this,void 0,void 0,function*(){try{const a=yield Vt(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:i,paths:r},{headers:this.headers}),c=l?.download?`&download=${l.download===!0?"":l.download}`:"";return{data:a.map(f=>Object.assign(Object.assign({},f),{signedUrl:f.signedURL?encodeURI(`${this.url}${f.signedURL}${c}`):null})),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}download(r,i){return et(this,void 0,void 0,function*(){const a=typeof i?.transform<"u"?"render/image/authenticated":"object",c=this.transformOptsToQueryString(i?.transform||{}),f=c?`?${c}`:"";try{const h=this._getFinalPath(r);return{data:yield(yield qs(this.fetch,`${this.url}/${a}/${h}${f}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(h){if(Ae(h))return{data:null,error:h};throw h}})}info(r){return et(this,void 0,void 0,function*(){const i=this._getFinalPath(r);try{const l=yield qs(this.fetch,`${this.url}/object/info/${i}`,{headers:this.headers});return{data:da(l),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}exists(r){return et(this,void 0,void 0,function*(){const i=this._getFinalPath(r);try{return yield uv(this.fetch,`${this.url}/object/${i}`,{headers:this.headers}),{data:!0,error:null}}catch(l){if(Ae(l)&&l instanceof ca){const a=l.originalError;if([400,404].includes(a?.status))return{data:!1,error:l}}throw l}})}getPublicUrl(r,i){const l=this._getFinalPath(r),a=[],c=i?.download?`download=${i.download===!0?"":i.download}`:"";c!==""&&a.push(c);const h=typeof i?.transform<"u"?"render/image":"object",p=this.transformOptsToQueryString(i?.transform||{});p!==""&&a.push(p);let g=a.join("&");return g!==""&&(g=`?${g}`),{data:{publicUrl:encodeURI(`${this.url}/${h}/public/${l}${g}`)}}}remove(r){return et(this,void 0,void 0,function*(){try{return{data:yield Uf(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:r},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}list(r,i,l){return et(this,void 0,void 0,function*(){try{const a=Object.assign(Object.assign(Object.assign({},cv),i),{prefix:r||""});return{data:yield Vt(this.fetch,`${this.url}/object/list/${this.bucketId}`,a,{headers:this.headers},l),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}encodeMetadata(r){return JSON.stringify(r)}toBase64(r){return typeof Buffer<"u"?Buffer.from(r).toString("base64"):btoa(r)}_getFinalPath(r){return`${this.bucketId}/${r.replace(/^\/+/,"")}`}_removeEmptyFolders(r){return r.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(r){const i=[];return r.width&&i.push(`width=${r.width}`),r.height&&i.push(`height=${r.height}`),r.resize&&i.push(`resize=${r.resize}`),r.format&&i.push(`format=${r.format}`),r.quality&&i.push(`quality=${r.quality}`),i.join("&")}}const fv="2.10.4",hv={"X-Client-Info":`storage-js/${fv}`};var mr=function(o,r,i,l){function a(c){return c instanceof i?c:new i(function(f){f(c)})}return new(i||(i=Promise))(function(c,f){function h(v){try{g(l.next(v))}catch(w){f(w)}}function p(v){try{g(l.throw(v))}catch(w){f(w)}}function g(v){v.done?c(v.value):a(v.value).then(h,p)}g((l=l.apply(o,r||[])).next())})};class pv{constructor(r,i={},l,a){const c=new URL(r);a?.useNewHostname&&/supabase\.(co|in|red)$/.test(c.hostname)&&!c.hostname.includes("storage.supabase.")&&(c.hostname=c.hostname.replace("supabase.","storage.supabase.")),this.url=c.href,this.headers=Object.assign(Object.assign({},hv),i),this.fetch=Df(l)}listBuckets(){return mr(this,void 0,void 0,function*(){try{return{data:yield qs(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(r){if(Ae(r))return{data:null,error:r};throw r}})}getBucket(r){return mr(this,void 0,void 0,function*(){try{return{data:yield qs(this.fetch,`${this.url}/bucket/${r}`,{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}createBucket(r,i={public:!1}){return mr(this,void 0,void 0,function*(){try{return{data:yield Vt(this.fetch,`${this.url}/bucket`,{id:r,name:r,type:i.type,public:i.public,file_size_limit:i.fileSizeLimit,allowed_mime_types:i.allowedMimeTypes},{headers:this.headers}),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}updateBucket(r,i){return mr(this,void 0,void 0,function*(){try{return{data:yield fa(this.fetch,`${this.url}/bucket/${r}`,{id:r,name:r,public:i.public,file_size_limit:i.fileSizeLimit,allowed_mime_types:i.allowedMimeTypes},{headers:this.headers}),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}emptyBucket(r){return mr(this,void 0,void 0,function*(){try{return{data:yield Vt(this.fetch,`${this.url}/bucket/${r}/empty`,{},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}deleteBucket(r){return mr(this,void 0,void 0,function*(){try{return{data:yield Uf(this.fetch,`${this.url}/bucket/${r}`,{},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}}class mv extends pv{constructor(r,i={},l,a){super(r,i,l,a)}from(r){return new dv(this.url,this.headers,r,this.fetch)}}const gv="2.53.0";let mi="";typeof Deno<"u"?mi="deno":typeof document<"u"?mi="web":typeof navigator<"u"&&navigator.product==="ReactNative"?mi="react-native":mi="node";const vv={"X-Client-Info":`supabase-js-${mi}/${gv}`},yv={headers:vv},wv={schema:"public"},_v={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},kv={};var Sv=function(o,r,i,l){function a(c){return c instanceof i?c:new i(function(f){f(c)})}return new(i||(i=Promise))(function(c,f){function h(v){try{g(l.next(v))}catch(w){f(w)}}function p(v){try{g(l.throw(v))}catch(w){f(w)}}function g(v){v.done?c(v.value):a(v.value).then(h,p)}g((l=l.apply(o,r||[])).next())})};const Ev=o=>{let r;return o?r=o:typeof fetch>"u"?r=jf:r=fetch,(...i)=>r(...i)},xv=()=>typeof Headers>"u"?Tf:Headers,Cv=(o,r,i)=>{const l=Ev(i),a=xv();return(c,f)=>Sv(void 0,void 0,void 0,function*(){var h;const p=(h=yield r())!==null&&h!==void 0?h:o;let g=new a(f?.headers);return g.has("apikey")||g.set("apikey",o),g.has("Authorization")||g.set("Authorization",`Bearer ${p}`),l(c,Object.assign(Object.assign({},f),{headers:g}))})};var Pv=function(o,r,i,l){function a(c){return c instanceof i?c:new i(function(f){f(c)})}return new(i||(i=Promise))(function(c,f){function h(v){try{g(l.next(v))}catch(w){f(w)}}function p(v){try{g(l.throw(v))}catch(w){f(w)}}function g(v){v.done?c(v.value):a(v.value).then(h,p)}g((l=l.apply(o,r||[])).next())})};function jv(o){return o.endsWith("/")?o:o+"/"}function Tv(o,r){var i,l;const{db:a,auth:c,realtime:f,global:h}=o,{db:p,auth:g,realtime:v,global:w}=r,S={db:Object.assign(Object.assign({},p),a),auth:Object.assign(Object.assign({},g),c),realtime:Object.assign(Object.assign({},v),f),storage:{},global:Object.assign(Object.assign(Object.assign({},w),h),{headers:Object.assign(Object.assign({},(i=w?.headers)!==null&&i!==void 0?i:{}),(l=h?.headers)!==null&&l!==void 0?l:{})}),accessToken:()=>Pv(this,void 0,void 0,function*(){return""})};return o.accessToken?S.accessToken=o.accessToken:delete S.accessToken,S}const zf="2.71.1",_r=30*1e3,ha=3,ea=ha*_r,Rv="http://localhost:9999",Ov="supabase.auth.token",Nv={"X-Client-Info":`gotrue-js/${zf}`},pa="X-Supabase-Api-Version",Mf={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},Lv=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,$v=600*1e3;class ja extends Error{constructor(r,i,l){super(r),this.__isAuthError=!0,this.name="AuthError",this.status=i,this.code=l}}function te(o){return typeof o=="object"&&o!==null&&"__isAuthError"in o}class Iv extends ja{constructor(r,i,l){super(r,i,l),this.name="AuthApiError",this.status=i,this.code=l}}function Av(o){return te(o)&&o.name==="AuthApiError"}class Ff extends ja{constructor(r,i){super(r),this.name="AuthUnknownError",this.originalError=i}}class wn extends ja{constructor(r,i,l,a){super(r,l,a),this.name=i,this.status=l}}class gn extends wn{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function bv(o){return te(o)&&o.name==="AuthSessionMissingError"}class zs extends wn{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Ms extends wn{constructor(r){super(r,"AuthInvalidCredentialsError",400,void 0)}}class Fs extends wn{constructor(r,i=null){super(r,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=i}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Dv(o){return te(o)&&o.name==="AuthImplicitGrantRedirectError"}class ef extends wn{constructor(r,i=null){super(r,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=i}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ma extends wn{constructor(r,i){super(r,"AuthRetryableFetchError",i,void 0)}}function ta(o){return te(o)&&o.name==="AuthRetryableFetchError"}class tf extends wn{constructor(r,i,l){super(r,"AuthWeakPasswordError",i,"weak_password"),this.reasons=l}}class ga extends wn{constructor(r){super(r,"AuthInvalidJwtError",400,"invalid_jwt")}}const Vs="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),nf=` 	
\r=`.split(""),Uv=(()=>{const o=new Array(128);for(let r=0;r<o.length;r+=1)o[r]=-1;for(let r=0;r<nf.length;r+=1)o[nf[r].charCodeAt(0)]=-2;for(let r=0;r<Vs.length;r+=1)o[Vs[r].charCodeAt(0)]=r;return o})();function rf(o,r,i){if(o!==null)for(r.queue=r.queue<<8|o,r.queuedBits+=8;r.queuedBits>=6;){const l=r.queue>>r.queuedBits-6&63;i(Vs[l]),r.queuedBits-=6}else if(r.queuedBits>0)for(r.queue=r.queue<<6-r.queuedBits,r.queuedBits=6;r.queuedBits>=6;){const l=r.queue>>r.queuedBits-6&63;i(Vs[l]),r.queuedBits-=6}}function Bf(o,r,i){const l=Uv[o];if(l>-1)for(r.queue=r.queue<<6|l,r.queuedBits+=6;r.queuedBits>=8;)i(r.queue>>r.queuedBits-8&255),r.queuedBits-=8;else{if(l===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(o)}"`)}}function sf(o){const r=[],i=f=>{r.push(String.fromCodePoint(f))},l={utf8seq:0,codepoint:0},a={queue:0,queuedBits:0},c=f=>{Fv(f,l,i)};for(let f=0;f<o.length;f+=1)Bf(o.charCodeAt(f),a,c);return r.join("")}function zv(o,r){if(o<=127){r(o);return}else if(o<=2047){r(192|o>>6),r(128|o&63);return}else if(o<=65535){r(224|o>>12),r(128|o>>6&63),r(128|o&63);return}else if(o<=1114111){r(240|o>>18),r(128|o>>12&63),r(128|o>>6&63),r(128|o&63);return}throw new Error(`Unrecognized Unicode codepoint: ${o.toString(16)}`)}function Mv(o,r){for(let i=0;i<o.length;i+=1){let l=o.charCodeAt(i);if(l>55295&&l<=56319){const a=(l-55296)*1024&65535;l=(o.charCodeAt(i+1)-56320&65535|a)+65536,i+=1}zv(l,r)}}function Fv(o,r,i){if(r.utf8seq===0){if(o<=127){i(o);return}for(let l=1;l<6;l+=1)if((o>>7-l&1)===0){r.utf8seq=l;break}if(r.utf8seq===2)r.codepoint=o&31;else if(r.utf8seq===3)r.codepoint=o&15;else if(r.utf8seq===4)r.codepoint=o&7;else throw new Error("Invalid UTF-8 sequence");r.utf8seq-=1}else if(r.utf8seq>0){if(o<=127)throw new Error("Invalid UTF-8 sequence");r.codepoint=r.codepoint<<6|o&63,r.utf8seq-=1,r.utf8seq===0&&i(r.codepoint)}}function Bv(o){const r=[],i={queue:0,queuedBits:0},l=a=>{r.push(a)};for(let a=0;a<o.length;a+=1)Bf(o.charCodeAt(a),i,l);return new Uint8Array(r)}function Wv(o){const r=[];return Mv(o,i=>r.push(i)),new Uint8Array(r)}function Hv(o){const r=[],i={queue:0,queuedBits:0},l=a=>{r.push(a)};return o.forEach(a=>rf(a,i,l)),rf(null,i,l),r.join("")}function qv(o){return Math.round(Date.now()/1e3)+o}function Vv(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(o){const r=Math.random()*16|0;return(o=="x"?r:r&3|8).toString(16)})}const Et=()=>typeof window<"u"&&typeof document<"u",$n={tested:!1,writable:!1},Wf=()=>{if(!Et())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if($n.tested)return $n.writable;const o=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(o,o),globalThis.localStorage.removeItem(o),$n.tested=!0,$n.writable=!0}catch{$n.tested=!0,$n.writable=!1}return $n.writable};function Kv(o){const r={},i=new URL(o);if(i.hash&&i.hash[0]==="#")try{new URLSearchParams(i.hash.substring(1)).forEach((a,c)=>{r[c]=a})}catch{}return i.searchParams.forEach((l,a)=>{r[a]=l}),r}const Hf=o=>{let r;return o?r=o:typeof fetch>"u"?r=(...i)=>xi(async()=>{const{default:l}=await Promise.resolve().then(()=>Cr);return{default:l}},void 0).then(({default:l})=>l(...i)):r=fetch,(...i)=>r(...i)},Jv=o=>typeof o=="object"&&o!==null&&"status"in o&&"ok"in o&&"json"in o&&typeof o.json=="function",kr=async(o,r,i)=>{await o.setItem(r,JSON.stringify(i))},In=async(o,r)=>{const i=await o.getItem(r);if(!i)return null;try{return JSON.parse(i)}catch{return i}},mn=async(o,r)=>{await o.removeItem(r)};class Qs{constructor(){this.promise=new Qs.promiseConstructor((r,i)=>{this.resolve=r,this.reject=i})}}Qs.promiseConstructor=Promise;function na(o){const r=o.split(".");if(r.length!==3)throw new ga("Invalid JWT structure");for(let l=0;l<r.length;l++)if(!Lv.test(r[l]))throw new ga("JWT not in base64url format");return{header:JSON.parse(sf(r[0])),payload:JSON.parse(sf(r[1])),signature:Bv(r[2]),raw:{header:r[0],payload:r[1]}}}async function Gv(o){return await new Promise(r=>{setTimeout(()=>r(null),o)})}function Qv(o,r){return new Promise((l,a)=>{(async()=>{for(let c=0;c<1/0;c++)try{const f=await o(c);if(!r(c,null,f)){l(f);return}}catch(f){if(!r(c,f)){a(f);return}}})()})}function Yv(o){return("0"+o.toString(16)).substr(-2)}function Xv(){const r=new Uint32Array(56);if(typeof crypto>"u"){const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",l=i.length;let a="";for(let c=0;c<56;c++)a+=i.charAt(Math.floor(Math.random()*l));return a}return crypto.getRandomValues(r),Array.from(r,Yv).join("")}async function Zv(o){const i=new TextEncoder().encode(o),l=await crypto.subtle.digest("SHA-256",i),a=new Uint8Array(l);return Array.from(a).map(c=>String.fromCharCode(c)).join("")}async function ey(o){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),o;const i=await Zv(o);return btoa(i).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function gr(o,r,i=!1){const l=Xv();let a=l;i&&(a+="/PASSWORD_RECOVERY"),await kr(o,`${r}-code-verifier`,a);const c=await ey(l);return[c,l===c?"plain":"s256"]}const ty=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function ny(o){const r=o.headers.get(pa);if(!r||!r.match(ty))return null;try{return new Date(`${r}T00:00:00.0Z`)}catch{return null}}function ry(o){if(!o)throw new Error("Missing exp claim");const r=Math.floor(Date.now()/1e3);if(o<=r)throw new Error("JWT has expired")}function iy(o){switch(o){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const sy=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function vr(o){if(!sy.test(o))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function ra(){const o={};return new Proxy(o,{get:(r,i)=>{if(i==="__isUserNotAvailableProxy")return!0;if(typeof i=="symbol"){const l=i.toString();if(l==="Symbol(Symbol.toPrimitive)"||l==="Symbol(Symbol.toStringTag)"||l==="Symbol(util.inspect.custom)")return}throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${i}" property of the session object is not supported. Please use getUser() instead.`)},set:(r,i)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${i}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(r,i)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${i}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}function of(o){return JSON.parse(JSON.stringify(o))}var oy=function(o,r){var i={};for(var l in o)Object.prototype.hasOwnProperty.call(o,l)&&r.indexOf(l)<0&&(i[l]=o[l]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,l=Object.getOwnPropertySymbols(o);a<l.length;a++)r.indexOf(l[a])<0&&Object.prototype.propertyIsEnumerable.call(o,l[a])&&(i[l[a]]=o[l[a]]);return i};const bn=o=>o.msg||o.message||o.error_description||o.error||JSON.stringify(o),ly=[502,503,504];async function lf(o){var r;if(!Jv(o))throw new ma(bn(o),0);if(ly.includes(o.status))throw new ma(bn(o),o.status);let i;try{i=await o.json()}catch(c){throw new Ff(bn(c),c)}let l;const a=ny(o);if(a&&a.getTime()>=Mf["2024-01-01"].timestamp&&typeof i=="object"&&i&&typeof i.code=="string"?l=i.code:typeof i=="object"&&i&&typeof i.error_code=="string"&&(l=i.error_code),l){if(l==="weak_password")throw new tf(bn(i),o.status,((r=i.weak_password)===null||r===void 0?void 0:r.reasons)||[]);if(l==="session_not_found")throw new gn}else if(typeof i=="object"&&i&&typeof i.weak_password=="object"&&i.weak_password&&Array.isArray(i.weak_password.reasons)&&i.weak_password.reasons.length&&i.weak_password.reasons.reduce((c,f)=>c&&typeof f=="string",!0))throw new tf(bn(i),o.status,i.weak_password.reasons);throw new Iv(bn(i),o.status||500,l)}const ay=(o,r,i,l)=>{const a={method:o,headers:r?.headers||{}};return o==="GET"?a:(a.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},r?.headers),a.body=JSON.stringify(l),Object.assign(Object.assign({},a),i))};async function oe(o,r,i,l){var a;const c=Object.assign({},l?.headers);c[pa]||(c[pa]=Mf["2024-01-01"].name),l?.jwt&&(c.Authorization=`Bearer ${l.jwt}`);const f=(a=l?.query)!==null&&a!==void 0?a:{};l?.redirectTo&&(f.redirect_to=l.redirectTo);const h=Object.keys(f).length?"?"+new URLSearchParams(f).toString():"",p=await uy(o,r,i+h,{headers:c,noResolveJson:l?.noResolveJson},{},l?.body);return l?.xform?l?.xform(p):{data:Object.assign({},p),error:null}}async function uy(o,r,i,l,a,c){const f=ay(r,l,a,c);let h;try{h=await o(i,Object.assign({},f))}catch(p){throw console.error(p),new ma(bn(p),0)}if(h.ok||await lf(h),l?.noResolveJson)return h;try{return await h.json()}catch(p){await lf(p)}}function Ht(o){var r;let i=null;hy(o)&&(i=Object.assign({},o),o.expires_at||(i.expires_at=qv(o.expires_in)));const l=(r=o.user)!==null&&r!==void 0?r:o;return{data:{session:i,user:l},error:null}}function af(o){const r=Ht(o);return!r.error&&o.weak_password&&typeof o.weak_password=="object"&&Array.isArray(o.weak_password.reasons)&&o.weak_password.reasons.length&&o.weak_password.message&&typeof o.weak_password.message=="string"&&o.weak_password.reasons.reduce((i,l)=>i&&typeof l=="string",!0)&&(r.data.weak_password=o.weak_password),r}function vn(o){var r;return{data:{user:(r=o.user)!==null&&r!==void 0?r:o},error:null}}function cy(o){return{data:o,error:null}}function dy(o){const{action_link:r,email_otp:i,hashed_token:l,redirect_to:a,verification_type:c}=o,f=oy(o,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),h={action_link:r,email_otp:i,hashed_token:l,redirect_to:a,verification_type:c},p=Object.assign({},f);return{data:{properties:h,user:p},error:null}}function fy(o){return o}function hy(o){return o.access_token&&o.refresh_token&&o.expires_in}const ia=["global","local","others"];var py=function(o,r){var i={};for(var l in o)Object.prototype.hasOwnProperty.call(o,l)&&r.indexOf(l)<0&&(i[l]=o[l]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,l=Object.getOwnPropertySymbols(o);a<l.length;a++)r.indexOf(l[a])<0&&Object.prototype.propertyIsEnumerable.call(o,l[a])&&(i[l[a]]=o[l[a]]);return i};class my{constructor({url:r="",headers:i={},fetch:l}){this.url=r,this.headers=i,this.fetch=Hf(l),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(r,i=ia[0]){if(ia.indexOf(i)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${ia.join(", ")}`);try{return await oe(this.fetch,"POST",`${this.url}/logout?scope=${i}`,{headers:this.headers,jwt:r,noResolveJson:!0}),{data:null,error:null}}catch(l){if(te(l))return{data:null,error:l};throw l}}async inviteUserByEmail(r,i={}){try{return await oe(this.fetch,"POST",`${this.url}/invite`,{body:{email:r,data:i.data},headers:this.headers,redirectTo:i.redirectTo,xform:vn})}catch(l){if(te(l))return{data:{user:null},error:l};throw l}}async generateLink(r){try{const{options:i}=r,l=py(r,["options"]),a=Object.assign(Object.assign({},l),i);return"newEmail"in l&&(a.new_email=l?.newEmail,delete a.newEmail),await oe(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:a,headers:this.headers,xform:dy,redirectTo:i?.redirectTo})}catch(i){if(te(i))return{data:{properties:null,user:null},error:i};throw i}}async createUser(r){try{return await oe(this.fetch,"POST",`${this.url}/admin/users`,{body:r,headers:this.headers,xform:vn})}catch(i){if(te(i))return{data:{user:null},error:i};throw i}}async listUsers(r){var i,l,a,c,f,h,p;try{const g={nextPage:null,lastPage:0,total:0},v=await oe(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(l=(i=r?.page)===null||i===void 0?void 0:i.toString())!==null&&l!==void 0?l:"",per_page:(c=(a=r?.perPage)===null||a===void 0?void 0:a.toString())!==null&&c!==void 0?c:""},xform:fy});if(v.error)throw v.error;const w=await v.json(),S=(f=v.headers.get("x-total-count"))!==null&&f!==void 0?f:0,j=(p=(h=v.headers.get("link"))===null||h===void 0?void 0:h.split(","))!==null&&p!==void 0?p:[];return j.length>0&&(j.forEach(O=>{const $=parseInt(O.split(";")[0].split("=")[1].substring(0,1)),E=JSON.parse(O.split(";")[1].split("=")[1]);g[`${E}Page`]=$}),g.total=parseInt(S)),{data:Object.assign(Object.assign({},w),g),error:null}}catch(g){if(te(g))return{data:{users:[]},error:g};throw g}}async getUserById(r){vr(r);try{return await oe(this.fetch,"GET",`${this.url}/admin/users/${r}`,{headers:this.headers,xform:vn})}catch(i){if(te(i))return{data:{user:null},error:i};throw i}}async updateUserById(r,i){vr(r);try{return await oe(this.fetch,"PUT",`${this.url}/admin/users/${r}`,{body:i,headers:this.headers,xform:vn})}catch(l){if(te(l))return{data:{user:null},error:l};throw l}}async deleteUser(r,i=!1){vr(r);try{return await oe(this.fetch,"DELETE",`${this.url}/admin/users/${r}`,{headers:this.headers,body:{should_soft_delete:i},xform:vn})}catch(l){if(te(l))return{data:{user:null},error:l};throw l}}async _listFactors(r){vr(r.userId);try{const{data:i,error:l}=await oe(this.fetch,"GET",`${this.url}/admin/users/${r.userId}/factors`,{headers:this.headers,xform:a=>({data:{factors:a},error:null})});return{data:i,error:l}}catch(i){if(te(i))return{data:null,error:i};throw i}}async _deleteFactor(r){vr(r.userId),vr(r.id);try{return{data:await oe(this.fetch,"DELETE",`${this.url}/admin/users/${r.userId}/factors/${r.id}`,{headers:this.headers}),error:null}}catch(i){if(te(i))return{data:null,error:i};throw i}}}function uf(o={}){return{getItem:r=>o[r]||null,setItem:(r,i)=>{o[r]=i},removeItem:r=>{delete o[r]}}}function gy(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const yr={debug:!!(globalThis&&Wf()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class qf extends Error{constructor(r){super(r),this.isAcquireTimeout=!0}}class vy extends qf{}async function yy(o,r,i){yr.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",o,r);const l=new globalThis.AbortController;return r>0&&setTimeout(()=>{l.abort(),yr.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",o)},r),await Promise.resolve().then(()=>globalThis.navigator.locks.request(o,r===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:l.signal},async a=>{if(a){yr.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",o,a.name);try{return await i()}finally{yr.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",o,a.name)}}else{if(r===0)throw yr.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",o),new vy(`Acquiring an exclusive Navigator LockManager lock "${o}" immediately failed`);if(yr.debug)try{const c=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(c,null,"  "))}catch(c){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",c)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await i()}}))}gy();const wy={url:Rv,storageKey:Ov,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Nv,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function cf(o,r,i){return await i()}const wr={};class wi{constructor(r){var i,l;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=wi.nextInstanceID,wi.nextInstanceID+=1,this.instanceID>0&&Et()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const a=Object.assign(Object.assign({},wy),r);if(this.logDebugMessages=!!a.debug,typeof a.debug=="function"&&(this.logger=a.debug),this.persistSession=a.persistSession,this.storageKey=a.storageKey,this.autoRefreshToken=a.autoRefreshToken,this.admin=new my({url:a.url,headers:a.headers,fetch:a.fetch}),this.url=a.url,this.headers=a.headers,this.fetch=Hf(a.fetch),this.lock=a.lock||cf,this.detectSessionInUrl=a.detectSessionInUrl,this.flowType=a.flowType,this.hasCustomAuthorizationHeader=a.hasCustomAuthorizationHeader,a.lock?this.lock=a.lock:Et()&&(!((i=globalThis?.navigator)===null||i===void 0)&&i.locks)?this.lock=yy:this.lock=cf,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(a.storage?this.storage=a.storage:Wf()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=uf(this.memoryStorage)),a.userStorage&&(this.userStorage=a.userStorage)):(this.memoryStorage={},this.storage=uf(this.memoryStorage)),Et()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(c){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",c)}(l=this.broadcastChannel)===null||l===void 0||l.addEventListener("message",async c=>{this._debug("received broadcast notification from other tab or client",c),await this._notifyAllSubscribers(c.data.event,c.data.session,!1)})}this.initialize()}get jwks(){var r,i;return(i=(r=wr[this.storageKey])===null||r===void 0?void 0:r.jwks)!==null&&i!==void 0?i:{keys:[]}}set jwks(r){wr[this.storageKey]=Object.assign(Object.assign({},wr[this.storageKey]),{jwks:r})}get jwks_cached_at(){var r,i;return(i=(r=wr[this.storageKey])===null||r===void 0?void 0:r.cachedAt)!==null&&i!==void 0?i:Number.MIN_SAFE_INTEGER}set jwks_cached_at(r){wr[this.storageKey]=Object.assign(Object.assign({},wr[this.storageKey]),{cachedAt:r})}_debug(...r){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${zf}) ${new Date().toISOString()}`,...r),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var r;try{const i=Kv(window.location.href);let l="none";if(this._isImplicitGrantCallback(i)?l="implicit":await this._isPKCECallback(i)&&(l="pkce"),Et()&&this.detectSessionInUrl&&l!=="none"){const{data:a,error:c}=await this._getSessionFromURL(i,l);if(c){if(this._debug("#_initialize()","error detecting session from URL",c),Dv(c)){const p=(r=c.details)===null||r===void 0?void 0:r.code;if(p==="identity_already_exists"||p==="identity_not_found"||p==="single_identity_not_deletable")return{error:c}}return await this._removeSession(),{error:c}}const{session:f,redirectType:h}=a;return this._debug("#_initialize()","detected session in URL",f,"redirect type",h),await this._saveSession(f),setTimeout(async()=>{h==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",f):await this._notifyAllSubscribers("SIGNED_IN",f)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(i){return te(i)?{error:i}:{error:new Ff("Unexpected error during initialization",i)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(r){var i,l,a;try{const c=await oe(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(l=(i=r?.options)===null||i===void 0?void 0:i.data)!==null&&l!==void 0?l:{},gotrue_meta_security:{captcha_token:(a=r?.options)===null||a===void 0?void 0:a.captchaToken}},xform:Ht}),{data:f,error:h}=c;if(h||!f)return{data:{user:null,session:null},error:h};const p=f.session,g=f.user;return f.session&&(await this._saveSession(f.session),await this._notifyAllSubscribers("SIGNED_IN",p)),{data:{user:g,session:p},error:null}}catch(c){if(te(c))return{data:{user:null,session:null},error:c};throw c}}async signUp(r){var i,l,a;try{let c;if("email"in r){const{email:v,password:w,options:S}=r;let j=null,O=null;this.flowType==="pkce"&&([j,O]=await gr(this.storage,this.storageKey)),c=await oe(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:S?.emailRedirectTo,body:{email:v,password:w,data:(i=S?.data)!==null&&i!==void 0?i:{},gotrue_meta_security:{captcha_token:S?.captchaToken},code_challenge:j,code_challenge_method:O},xform:Ht})}else if("phone"in r){const{phone:v,password:w,options:S}=r;c=await oe(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:v,password:w,data:(l=S?.data)!==null&&l!==void 0?l:{},channel:(a=S?.channel)!==null&&a!==void 0?a:"sms",gotrue_meta_security:{captcha_token:S?.captchaToken}},xform:Ht})}else throw new Ms("You must provide either an email or phone number and a password");const{data:f,error:h}=c;if(h||!f)return{data:{user:null,session:null},error:h};const p=f.session,g=f.user;return f.session&&(await this._saveSession(f.session),await this._notifyAllSubscribers("SIGNED_IN",p)),{data:{user:g,session:p},error:null}}catch(c){if(te(c))return{data:{user:null,session:null},error:c};throw c}}async signInWithPassword(r){try{let i;if("email"in r){const{email:c,password:f,options:h}=r;i=await oe(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:c,password:f,gotrue_meta_security:{captcha_token:h?.captchaToken}},xform:af})}else if("phone"in r){const{phone:c,password:f,options:h}=r;i=await oe(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:c,password:f,gotrue_meta_security:{captcha_token:h?.captchaToken}},xform:af})}else throw new Ms("You must provide either an email or phone number and a password");const{data:l,error:a}=i;return a?{data:{user:null,session:null},error:a}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new zs}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:Object.assign({user:l.user,session:l.session},l.weak_password?{weakPassword:l.weak_password}:null),error:a})}catch(i){if(te(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithOAuth(r){var i,l,a,c;return await this._handleProviderSignIn(r.provider,{redirectTo:(i=r.options)===null||i===void 0?void 0:i.redirectTo,scopes:(l=r.options)===null||l===void 0?void 0:l.scopes,queryParams:(a=r.options)===null||a===void 0?void 0:a.queryParams,skipBrowserRedirect:(c=r.options)===null||c===void 0?void 0:c.skipBrowserRedirect})}async exchangeCodeForSession(r){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(r))}async signInWithWeb3(r){const{chain:i}=r;if(i==="solana")return await this.signInWithSolana(r);throw new Error(`@supabase/auth-js: Unsupported chain "${i}"`)}async signInWithSolana(r){var i,l,a,c,f,h,p,g,v,w,S,j;let O,$;if("message"in r)O=r.message,$=r.signature;else{const{chain:E,wallet:N,statement:J,options:D}=r;let W;if(Et())if(typeof N=="object")W=N;else{const ee=window;if("solana"in ee&&typeof ee.solana=="object"&&("signIn"in ee.solana&&typeof ee.solana.signIn=="function"||"signMessage"in ee.solana&&typeof ee.solana.signMessage=="function"))W=ee.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof N!="object"||!D?.url)throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");W=N}const G=new URL((i=D?.url)!==null&&i!==void 0?i:window.location.href);if("signIn"in W&&W.signIn){const ee=await W.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},D?.signInWithSolana),{version:"1",domain:G.host,uri:G.href}),J?{statement:J}:null));let re;if(Array.isArray(ee)&&ee[0]&&typeof ee[0]=="object")re=ee[0];else if(ee&&typeof ee=="object"&&"signedMessage"in ee&&"signature"in ee)re=ee;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in re&&"signature"in re&&(typeof re.signedMessage=="string"||re.signedMessage instanceof Uint8Array)&&re.signature instanceof Uint8Array)O=typeof re.signedMessage=="string"?re.signedMessage:new TextDecoder().decode(re.signedMessage),$=re.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in W)||typeof W.signMessage!="function"||!("publicKey"in W)||typeof W!="object"||!W.publicKey||!("toBase58"in W.publicKey)||typeof W.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");O=[`${G.host} wants you to sign in with your Solana account:`,W.publicKey.toBase58(),...J?["",J,""]:[""],"Version: 1",`URI: ${G.href}`,`Issued At: ${(a=(l=D?.signInWithSolana)===null||l===void 0?void 0:l.issuedAt)!==null&&a!==void 0?a:new Date().toISOString()}`,...!((c=D?.signInWithSolana)===null||c===void 0)&&c.notBefore?[`Not Before: ${D.signInWithSolana.notBefore}`]:[],...!((f=D?.signInWithSolana)===null||f===void 0)&&f.expirationTime?[`Expiration Time: ${D.signInWithSolana.expirationTime}`]:[],...!((h=D?.signInWithSolana)===null||h===void 0)&&h.chainId?[`Chain ID: ${D.signInWithSolana.chainId}`]:[],...!((p=D?.signInWithSolana)===null||p===void 0)&&p.nonce?[`Nonce: ${D.signInWithSolana.nonce}`]:[],...!((g=D?.signInWithSolana)===null||g===void 0)&&g.requestId?[`Request ID: ${D.signInWithSolana.requestId}`]:[],...!((w=(v=D?.signInWithSolana)===null||v===void 0?void 0:v.resources)===null||w===void 0)&&w.length?["Resources",...D.signInWithSolana.resources.map(re=>`- ${re}`)]:[]].join(`
`);const ee=await W.signMessage(new TextEncoder().encode(O),"utf8");if(!ee||!(ee instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");$=ee}}try{const{data:E,error:N}=await oe(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:O,signature:Hv($)},!((S=r.options)===null||S===void 0)&&S.captchaToken?{gotrue_meta_security:{captcha_token:(j=r.options)===null||j===void 0?void 0:j.captchaToken}}:null),xform:Ht});if(N)throw N;return!E||!E.session||!E.user?{data:{user:null,session:null},error:new zs}:(E.session&&(await this._saveSession(E.session),await this._notifyAllSubscribers("SIGNED_IN",E.session)),{data:Object.assign({},E),error:N})}catch(E){if(te(E))return{data:{user:null,session:null},error:E};throw E}}async _exchangeCodeForSession(r){const i=await In(this.storage,`${this.storageKey}-code-verifier`),[l,a]=(i??"").split("/");try{const{data:c,error:f}=await oe(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:r,code_verifier:l},xform:Ht});if(await mn(this.storage,`${this.storageKey}-code-verifier`),f)throw f;return!c||!c.session||!c.user?{data:{user:null,session:null,redirectType:null},error:new zs}:(c.session&&(await this._saveSession(c.session),await this._notifyAllSubscribers("SIGNED_IN",c.session)),{data:Object.assign(Object.assign({},c),{redirectType:a??null}),error:f})}catch(c){if(te(c))return{data:{user:null,session:null,redirectType:null},error:c};throw c}}async signInWithIdToken(r){try{const{options:i,provider:l,token:a,access_token:c,nonce:f}=r,h=await oe(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:l,id_token:a,access_token:c,nonce:f,gotrue_meta_security:{captcha_token:i?.captchaToken}},xform:Ht}),{data:p,error:g}=h;return g?{data:{user:null,session:null},error:g}:!p||!p.session||!p.user?{data:{user:null,session:null},error:new zs}:(p.session&&(await this._saveSession(p.session),await this._notifyAllSubscribers("SIGNED_IN",p.session)),{data:p,error:g})}catch(i){if(te(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithOtp(r){var i,l,a,c,f;try{if("email"in r){const{email:h,options:p}=r;let g=null,v=null;this.flowType==="pkce"&&([g,v]=await gr(this.storage,this.storageKey));const{error:w}=await oe(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:h,data:(i=p?.data)!==null&&i!==void 0?i:{},create_user:(l=p?.shouldCreateUser)!==null&&l!==void 0?l:!0,gotrue_meta_security:{captcha_token:p?.captchaToken},code_challenge:g,code_challenge_method:v},redirectTo:p?.emailRedirectTo});return{data:{user:null,session:null},error:w}}if("phone"in r){const{phone:h,options:p}=r,{data:g,error:v}=await oe(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:h,data:(a=p?.data)!==null&&a!==void 0?a:{},create_user:(c=p?.shouldCreateUser)!==null&&c!==void 0?c:!0,gotrue_meta_security:{captcha_token:p?.captchaToken},channel:(f=p?.channel)!==null&&f!==void 0?f:"sms"}});return{data:{user:null,session:null,messageId:g?.message_id},error:v}}throw new Ms("You must provide either an email or phone number.")}catch(h){if(te(h))return{data:{user:null,session:null},error:h};throw h}}async verifyOtp(r){var i,l;try{let a,c;"options"in r&&(a=(i=r.options)===null||i===void 0?void 0:i.redirectTo,c=(l=r.options)===null||l===void 0?void 0:l.captchaToken);const{data:f,error:h}=await oe(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},r),{gotrue_meta_security:{captcha_token:c}}),redirectTo:a,xform:Ht});if(h)throw h;if(!f)throw new Error("An error occurred on token verification.");const p=f.session,g=f.user;return p?.access_token&&(await this._saveSession(p),await this._notifyAllSubscribers(r.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",p)),{data:{user:g,session:p},error:null}}catch(a){if(te(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithSSO(r){var i,l,a;try{let c=null,f=null;return this.flowType==="pkce"&&([c,f]=await gr(this.storage,this.storageKey)),await oe(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in r?{provider_id:r.providerId}:null),"domain"in r?{domain:r.domain}:null),{redirect_to:(l=(i=r.options)===null||i===void 0?void 0:i.redirectTo)!==null&&l!==void 0?l:void 0}),!((a=r?.options)===null||a===void 0)&&a.captchaToken?{gotrue_meta_security:{captcha_token:r.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:c,code_challenge_method:f}),headers:this.headers,xform:cy})}catch(c){if(te(c))return{data:null,error:c};throw c}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async r=>{const{data:{session:i},error:l}=r;if(l)throw l;if(!i)throw new gn;const{error:a}=await oe(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:i.access_token});return{data:{user:null,session:null},error:a}})}catch(r){if(te(r))return{data:{user:null,session:null},error:r};throw r}}async resend(r){try{const i=`${this.url}/resend`;if("email"in r){const{email:l,type:a,options:c}=r,{error:f}=await oe(this.fetch,"POST",i,{headers:this.headers,body:{email:l,type:a,gotrue_meta_security:{captcha_token:c?.captchaToken}},redirectTo:c?.emailRedirectTo});return{data:{user:null,session:null},error:f}}else if("phone"in r){const{phone:l,type:a,options:c}=r,{data:f,error:h}=await oe(this.fetch,"POST",i,{headers:this.headers,body:{phone:l,type:a,gotrue_meta_security:{captcha_token:c?.captchaToken}}});return{data:{user:null,session:null,messageId:f?.message_id},error:h}}throw new Ms("You must provide either an email or phone number and a type")}catch(i){if(te(i))return{data:{user:null,session:null},error:i};throw i}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async i=>i))}async _acquireLock(r,i){this._debug("#_acquireLock","begin",r);try{if(this.lockAcquired){const l=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),a=(async()=>(await l,await i()))();return this.pendingInLock.push((async()=>{try{await a}catch{}})()),a}return await this.lock(`lock:${this.storageKey}`,r,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const l=i();for(this.pendingInLock.push((async()=>{try{await l}catch{}})()),await l;this.pendingInLock.length;){const a=[...this.pendingInLock];await Promise.all(a),this.pendingInLock.splice(0,a.length)}return await l}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(r){this._debug("#_useSession","begin");try{const i=await this.__loadSession();return await r(i)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let r=null;const i=await In(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",i),i!==null&&(this._isValidSession(i)?r=i:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!r)return{data:{session:null},error:null};const l=r.expires_at?r.expires_at*1e3-Date.now()<ea:!1;if(this._debug("#__loadSession()",`session has${l?"":" not"} expired`,"expires_at",r.expires_at),!l){if(this.userStorage){const f=await In(this.userStorage,this.storageKey+"-user");f?.user?r.user=f.user:r.user=ra()}if(this.storage.isServer&&r.user){let f=this.suppressGetSessionWarning;r=new Proxy(r,{get:(p,g,v)=>(!f&&g==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),f=!0,this.suppressGetSessionWarning=!0),Reflect.get(p,g,v))})}return{data:{session:r},error:null}}const{session:a,error:c}=await this._callRefreshToken(r.refresh_token);return c?{data:{session:null},error:c}:{data:{session:a},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(r){return r?await this._getUser(r):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(r){try{return r?await oe(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:r,xform:vn}):await this._useSession(async i=>{var l,a,c;const{data:f,error:h}=i;if(h)throw h;return!(!((l=f.session)===null||l===void 0)&&l.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new gn}:await oe(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(c=(a=f.session)===null||a===void 0?void 0:a.access_token)!==null&&c!==void 0?c:void 0,xform:vn})})}catch(i){if(te(i))return bv(i)&&(await this._removeSession(),await mn(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:i};throw i}}async updateUser(r,i={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(r,i))}async _updateUser(r,i={}){try{return await this._useSession(async l=>{const{data:a,error:c}=l;if(c)throw c;if(!a.session)throw new gn;const f=a.session;let h=null,p=null;this.flowType==="pkce"&&r.email!=null&&([h,p]=await gr(this.storage,this.storageKey));const{data:g,error:v}=await oe(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:i?.emailRedirectTo,body:Object.assign(Object.assign({},r),{code_challenge:h,code_challenge_method:p}),jwt:f.access_token,xform:vn});if(v)throw v;return f.user=g.user,await this._saveSession(f),await this._notifyAllSubscribers("USER_UPDATED",f),{data:{user:f.user},error:null}})}catch(l){if(te(l))return{data:{user:null},error:l};throw l}}async setSession(r){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(r))}async _setSession(r){try{if(!r.access_token||!r.refresh_token)throw new gn;const i=Date.now()/1e3;let l=i,a=!0,c=null;const{payload:f}=na(r.access_token);if(f.exp&&(l=f.exp,a=l<=i),a){const{session:h,error:p}=await this._callRefreshToken(r.refresh_token);if(p)return{data:{user:null,session:null},error:p};if(!h)return{data:{user:null,session:null},error:null};c=h}else{const{data:h,error:p}=await this._getUser(r.access_token);if(p)throw p;c={access_token:r.access_token,refresh_token:r.refresh_token,user:h.user,token_type:"bearer",expires_in:l-i,expires_at:l},await this._saveSession(c),await this._notifyAllSubscribers("SIGNED_IN",c)}return{data:{user:c.user,session:c},error:null}}catch(i){if(te(i))return{data:{session:null,user:null},error:i};throw i}}async refreshSession(r){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(r))}async _refreshSession(r){try{return await this._useSession(async i=>{var l;if(!r){const{data:f,error:h}=i;if(h)throw h;r=(l=f.session)!==null&&l!==void 0?l:void 0}if(!r?.refresh_token)throw new gn;const{session:a,error:c}=await this._callRefreshToken(r.refresh_token);return c?{data:{user:null,session:null},error:c}:a?{data:{user:a.user,session:a},error:null}:{data:{user:null,session:null},error:null}})}catch(i){if(te(i))return{data:{user:null,session:null},error:i};throw i}}async _getSessionFromURL(r,i){try{if(!Et())throw new Fs("No browser detected.");if(r.error||r.error_description||r.error_code)throw new Fs(r.error_description||"Error in URL with unspecified error_description",{error:r.error||"unspecified_error",code:r.error_code||"unspecified_code"});switch(i){case"implicit":if(this.flowType==="pkce")throw new ef("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Fs("Not a valid implicit grant flow url.");break;default:}if(i==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!r.code)throw new ef("No code detected.");const{data:J,error:D}=await this._exchangeCodeForSession(r.code);if(D)throw D;const W=new URL(window.location.href);return W.searchParams.delete("code"),window.history.replaceState(window.history.state,"",W.toString()),{data:{session:J.session,redirectType:null},error:null}}const{provider_token:l,provider_refresh_token:a,access_token:c,refresh_token:f,expires_in:h,expires_at:p,token_type:g}=r;if(!c||!h||!f||!g)throw new Fs("No session defined in URL");const v=Math.round(Date.now()/1e3),w=parseInt(h);let S=v+w;p&&(S=parseInt(p));const j=S-v;j*1e3<=_r&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${j}s, should have been closer to ${w}s`);const O=S-w;v-O>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",O,S,v):v-O<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",O,S,v);const{data:$,error:E}=await this._getUser(c);if(E)throw E;const N={provider_token:l,provider_refresh_token:a,access_token:c,expires_in:w,expires_at:S,refresh_token:f,token_type:g,user:$.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:N,redirectType:r.type},error:null}}catch(l){if(te(l))return{data:{session:null,redirectType:null},error:l};throw l}}_isImplicitGrantCallback(r){return!!(r.access_token||r.error_description)}async _isPKCECallback(r){const i=await In(this.storage,`${this.storageKey}-code-verifier`);return!!(r.code&&i)}async signOut(r={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(r))}async _signOut({scope:r}={scope:"global"}){return await this._useSession(async i=>{var l;const{data:a,error:c}=i;if(c)return{error:c};const f=(l=a.session)===null||l===void 0?void 0:l.access_token;if(f){const{error:h}=await this.admin.signOut(f,r);if(h&&!(Av(h)&&(h.status===404||h.status===401||h.status===403)))return{error:h}}return r!=="others"&&(await this._removeSession(),await mn(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(r){const i=Vv(),l={id:i,callback:r,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",i),this.stateChangeEmitters.delete(i)}};return this._debug("#onAuthStateChange()","registered callback with id",i),this.stateChangeEmitters.set(i,l),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(i)})))(),{data:{subscription:l}}}async _emitInitialSession(r){return await this._useSession(async i=>{var l,a;try{const{data:{session:c},error:f}=i;if(f)throw f;await((l=this.stateChangeEmitters.get(r))===null||l===void 0?void 0:l.callback("INITIAL_SESSION",c)),this._debug("INITIAL_SESSION","callback id",r,"session",c)}catch(c){await((a=this.stateChangeEmitters.get(r))===null||a===void 0?void 0:a.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",r,"error",c),console.error(c)}})}async resetPasswordForEmail(r,i={}){let l=null,a=null;this.flowType==="pkce"&&([l,a]=await gr(this.storage,this.storageKey,!0));try{return await oe(this.fetch,"POST",`${this.url}/recover`,{body:{email:r,code_challenge:l,code_challenge_method:a,gotrue_meta_security:{captcha_token:i.captchaToken}},headers:this.headers,redirectTo:i.redirectTo})}catch(c){if(te(c))return{data:null,error:c};throw c}}async getUserIdentities(){var r;try{const{data:i,error:l}=await this.getUser();if(l)throw l;return{data:{identities:(r=i.user.identities)!==null&&r!==void 0?r:[]},error:null}}catch(i){if(te(i))return{data:null,error:i};throw i}}async linkIdentity(r){var i;try{const{data:l,error:a}=await this._useSession(async c=>{var f,h,p,g,v;const{data:w,error:S}=c;if(S)throw S;const j=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,r.provider,{redirectTo:(f=r.options)===null||f===void 0?void 0:f.redirectTo,scopes:(h=r.options)===null||h===void 0?void 0:h.scopes,queryParams:(p=r.options)===null||p===void 0?void 0:p.queryParams,skipBrowserRedirect:!0});return await oe(this.fetch,"GET",j,{headers:this.headers,jwt:(v=(g=w.session)===null||g===void 0?void 0:g.access_token)!==null&&v!==void 0?v:void 0})});if(a)throw a;return Et()&&!(!((i=r.options)===null||i===void 0)&&i.skipBrowserRedirect)&&window.location.assign(l?.url),{data:{provider:r.provider,url:l?.url},error:null}}catch(l){if(te(l))return{data:{provider:r.provider,url:null},error:l};throw l}}async unlinkIdentity(r){try{return await this._useSession(async i=>{var l,a;const{data:c,error:f}=i;if(f)throw f;return await oe(this.fetch,"DELETE",`${this.url}/user/identities/${r.identity_id}`,{headers:this.headers,jwt:(a=(l=c.session)===null||l===void 0?void 0:l.access_token)!==null&&a!==void 0?a:void 0})})}catch(i){if(te(i))return{data:null,error:i};throw i}}async _refreshAccessToken(r){const i=`#_refreshAccessToken(${r.substring(0,5)}...)`;this._debug(i,"begin");try{const l=Date.now();return await Qv(async a=>(a>0&&await Gv(200*Math.pow(2,a-1)),this._debug(i,"refreshing attempt",a),await oe(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:r},headers:this.headers,xform:Ht})),(a,c)=>{const f=200*Math.pow(2,a);return c&&ta(c)&&Date.now()+f-l<_r})}catch(l){if(this._debug(i,"error",l),te(l))return{data:{session:null,user:null},error:l};throw l}finally{this._debug(i,"end")}}_isValidSession(r){return typeof r=="object"&&r!==null&&"access_token"in r&&"refresh_token"in r&&"expires_at"in r}async _handleProviderSignIn(r,i){const l=await this._getUrlForProvider(`${this.url}/authorize`,r,{redirectTo:i.redirectTo,scopes:i.scopes,queryParams:i.queryParams});return this._debug("#_handleProviderSignIn()","provider",r,"options",i,"url",l),Et()&&!i.skipBrowserRedirect&&window.location.assign(l),{data:{provider:r,url:l},error:null}}async _recoverAndRefresh(){var r,i;const l="#_recoverAndRefresh()";this._debug(l,"begin");try{const a=await In(this.storage,this.storageKey);if(a&&this.userStorage){let f=await In(this.userStorage,this.storageKey+"-user");!this.storage.isServer&&Object.is(this.storage,this.userStorage)&&!f&&(f={user:a.user},await kr(this.userStorage,this.storageKey+"-user",f)),a.user=(r=f?.user)!==null&&r!==void 0?r:ra()}else if(a&&!a.user&&!a.user){const f=await In(this.storage,this.storageKey+"-user");f&&f?.user?(a.user=f.user,await mn(this.storage,this.storageKey+"-user"),await kr(this.storage,this.storageKey,a)):a.user=ra()}if(this._debug(l,"session from storage",a),!this._isValidSession(a)){this._debug(l,"session is not valid"),a!==null&&await this._removeSession();return}const c=((i=a.expires_at)!==null&&i!==void 0?i:1/0)*1e3-Date.now()<ea;if(this._debug(l,`session has${c?"":" not"} expired with margin of ${ea}s`),c){if(this.autoRefreshToken&&a.refresh_token){const{error:f}=await this._callRefreshToken(a.refresh_token);f&&(console.error(f),ta(f)||(this._debug(l,"refresh failed with a non-retryable error, removing the session",f),await this._removeSession()))}}else if(a.user&&a.user.__isUserNotAvailableProxy===!0)try{const{data:f,error:h}=await this._getUser(a.access_token);!h&&f?.user?(a.user=f.user,await this._saveSession(a),await this._notifyAllSubscribers("SIGNED_IN",a)):this._debug(l,"could not get user data, skipping SIGNED_IN notification")}catch(f){console.error("Error getting user data:",f),this._debug(l,"error getting user data, skipping SIGNED_IN notification",f)}else await this._notifyAllSubscribers("SIGNED_IN",a)}catch(a){this._debug(l,"error",a),console.error(a);return}finally{this._debug(l,"end")}}async _callRefreshToken(r){var i,l;if(!r)throw new gn;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const a=`#_callRefreshToken(${r.substring(0,5)}...)`;this._debug(a,"begin");try{this.refreshingDeferred=new Qs;const{data:c,error:f}=await this._refreshAccessToken(r);if(f)throw f;if(!c.session)throw new gn;await this._saveSession(c.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",c.session);const h={session:c.session,error:null};return this.refreshingDeferred.resolve(h),h}catch(c){if(this._debug(a,"error",c),te(c)){const f={session:null,error:c};return ta(c)||await this._removeSession(),(i=this.refreshingDeferred)===null||i===void 0||i.resolve(f),f}throw(l=this.refreshingDeferred)===null||l===void 0||l.reject(c),c}finally{this.refreshingDeferred=null,this._debug(a,"end")}}async _notifyAllSubscribers(r,i,l=!0){const a=`#_notifyAllSubscribers(${r})`;this._debug(a,"begin",i,`broadcast = ${l}`);try{this.broadcastChannel&&l&&this.broadcastChannel.postMessage({event:r,session:i});const c=[],f=Array.from(this.stateChangeEmitters.values()).map(async h=>{try{await h.callback(r,i)}catch(p){c.push(p)}});if(await Promise.all(f),c.length>0){for(let h=0;h<c.length;h+=1)console.error(c[h]);throw c[0]}}finally{this._debug(a,"end")}}async _saveSession(r){this._debug("#_saveSession()",r),this.suppressGetSessionWarning=!0;const i=Object.assign({},r),l=i.user&&i.user.__isUserNotAvailableProxy===!0;if(this.userStorage){!l&&i.user&&await kr(this.userStorage,this.storageKey+"-user",{user:i.user});const a=Object.assign({},i);delete a.user;const c=of(a);await kr(this.storage,this.storageKey,c)}else{const a=of(i);await kr(this.storage,this.storageKey,a)}}async _removeSession(){this._debug("#_removeSession()"),await mn(this.storage,this.storageKey),await mn(this.storage,this.storageKey+"-code-verifier"),await mn(this.storage,this.storageKey+"-user"),this.userStorage&&await mn(this.userStorage,this.storageKey+"-user"),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const r=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{r&&Et()&&window?.removeEventListener&&window.removeEventListener("visibilitychange",r)}catch(i){console.error("removing visibilitychange callback failed",i)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const r=setInterval(()=>this._autoRefreshTokenTick(),_r);this.autoRefreshTicker=r,r&&typeof r=="object"&&typeof r.unref=="function"?r.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(r),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const r=this.autoRefreshTicker;this.autoRefreshTicker=null,r&&clearInterval(r)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const r=Date.now();try{return await this._useSession(async i=>{const{data:{session:l}}=i;if(!l||!l.refresh_token||!l.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const a=Math.floor((l.expires_at*1e3-r)/_r);this._debug("#_autoRefreshTokenTick()",`access token expires in ${a} ticks, a tick lasts ${_r}ms, refresh threshold is ${ha} ticks`),a<=ha&&await this._callRefreshToken(l.refresh_token)})}catch(i){console.error("Auto refresh tick failed with error. This is likely a transient error.",i)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(r){if(r.isAcquireTimeout||r instanceof qf)this._debug("auto refresh token tick lock not available");else throw r}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Et()||!window?.addEventListener)return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window?.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(r){console.error("_handleVisibilityChange",r)}}async _onVisibilityChanged(r){const i=`#_onVisibilityChanged(${r})`;this._debug(i,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),r||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(i,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(r,i,l){const a=[`provider=${encodeURIComponent(i)}`];if(l?.redirectTo&&a.push(`redirect_to=${encodeURIComponent(l.redirectTo)}`),l?.scopes&&a.push(`scopes=${encodeURIComponent(l.scopes)}`),this.flowType==="pkce"){const[c,f]=await gr(this.storage,this.storageKey),h=new URLSearchParams({code_challenge:`${encodeURIComponent(c)}`,code_challenge_method:`${encodeURIComponent(f)}`});a.push(h.toString())}if(l?.queryParams){const c=new URLSearchParams(l.queryParams);a.push(c.toString())}return l?.skipBrowserRedirect&&a.push(`skip_http_redirect=${l.skipBrowserRedirect}`),`${r}?${a.join("&")}`}async _unenroll(r){try{return await this._useSession(async i=>{var l;const{data:a,error:c}=i;return c?{data:null,error:c}:await oe(this.fetch,"DELETE",`${this.url}/factors/${r.factorId}`,{headers:this.headers,jwt:(l=a?.session)===null||l===void 0?void 0:l.access_token})})}catch(i){if(te(i))return{data:null,error:i};throw i}}async _enroll(r){try{return await this._useSession(async i=>{var l,a;const{data:c,error:f}=i;if(f)return{data:null,error:f};const h=Object.assign({friendly_name:r.friendlyName,factor_type:r.factorType},r.factorType==="phone"?{phone:r.phone}:{issuer:r.issuer}),{data:p,error:g}=await oe(this.fetch,"POST",`${this.url}/factors`,{body:h,headers:this.headers,jwt:(l=c?.session)===null||l===void 0?void 0:l.access_token});return g?{data:null,error:g}:(r.factorType==="totp"&&(!((a=p?.totp)===null||a===void 0)&&a.qr_code)&&(p.totp.qr_code=`data:image/svg+xml;utf-8,${p.totp.qr_code}`),{data:p,error:null})})}catch(i){if(te(i))return{data:null,error:i};throw i}}async _verify(r){return this._acquireLock(-1,async()=>{try{return await this._useSession(async i=>{var l;const{data:a,error:c}=i;if(c)return{data:null,error:c};const{data:f,error:h}=await oe(this.fetch,"POST",`${this.url}/factors/${r.factorId}/verify`,{body:{code:r.code,challenge_id:r.challengeId},headers:this.headers,jwt:(l=a?.session)===null||l===void 0?void 0:l.access_token});return h?{data:null,error:h}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+f.expires_in},f)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",f),{data:f,error:h})})}catch(i){if(te(i))return{data:null,error:i};throw i}})}async _challenge(r){return this._acquireLock(-1,async()=>{try{return await this._useSession(async i=>{var l;const{data:a,error:c}=i;return c?{data:null,error:c}:await oe(this.fetch,"POST",`${this.url}/factors/${r.factorId}/challenge`,{body:{channel:r.channel},headers:this.headers,jwt:(l=a?.session)===null||l===void 0?void 0:l.access_token})})}catch(i){if(te(i))return{data:null,error:i};throw i}})}async _challengeAndVerify(r){const{data:i,error:l}=await this._challenge({factorId:r.factorId});return l?{data:null,error:l}:await this._verify({factorId:r.factorId,challengeId:i.id,code:r.code})}async _listFactors(){const{data:{user:r},error:i}=await this.getUser();if(i)return{data:null,error:i};const l=r?.factors||[],a=l.filter(f=>f.factor_type==="totp"&&f.status==="verified"),c=l.filter(f=>f.factor_type==="phone"&&f.status==="verified");return{data:{all:l,totp:a,phone:c},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async r=>{var i,l;const{data:{session:a},error:c}=r;if(c)return{data:null,error:c};if(!a)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:f}=na(a.access_token);let h=null;f.aal&&(h=f.aal);let p=h;((l=(i=a.user.factors)===null||i===void 0?void 0:i.filter(w=>w.status==="verified"))!==null&&l!==void 0?l:[]).length>0&&(p="aal2");const v=f.amr||[];return{data:{currentLevel:h,nextLevel:p,currentAuthenticationMethods:v},error:null}}))}async fetchJwk(r,i={keys:[]}){let l=i.keys.find(h=>h.kid===r);if(l)return l;const a=Date.now();if(l=this.jwks.keys.find(h=>h.kid===r),l&&this.jwks_cached_at+$v>a)return l;const{data:c,error:f}=await oe(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(f)throw f;return!c.keys||c.keys.length===0||(this.jwks=c,this.jwks_cached_at=a,l=c.keys.find(h=>h.kid===r),!l)?null:l}async getClaims(r,i={}){try{let l=r;if(!l){const{data:j,error:O}=await this.getSession();if(O||!j.session)return{data:null,error:O};l=j.session.access_token}const{header:a,payload:c,signature:f,raw:{header:h,payload:p}}=na(l);i?.allowExpired||ry(c.exp);const g=!a.alg||a.alg.startsWith("HS")||!a.kid||!("crypto"in globalThis&&"subtle"in globalThis.crypto)?null:await this.fetchJwk(a.kid,i?.keys?{keys:i.keys}:i?.jwks);if(!g){const{error:j}=await this.getUser(l);if(j)throw j;return{data:{claims:c,header:a,signature:f},error:null}}const v=iy(a.alg),w=await crypto.subtle.importKey("jwk",g,v,!0,["verify"]);if(!await crypto.subtle.verify(v,w,f,Wv(`${h}.${p}`)))throw new ga("Invalid JWT signature");return{data:{claims:c,header:a,signature:f},error:null}}catch(l){if(te(l))return{data:null,error:l};throw l}}}wi.nextInstanceID=0;const _y=wi;class ky extends _y{constructor(r){super(r)}}var Sy=function(o,r,i,l){function a(c){return c instanceof i?c:new i(function(f){f(c)})}return new(i||(i=Promise))(function(c,f){function h(v){try{g(l.next(v))}catch(w){f(w)}}function p(v){try{g(l.throw(v))}catch(w){f(w)}}function g(v){v.done?c(v.value):a(v.value).then(h,p)}g((l=l.apply(o,r||[])).next())})};class Ey{constructor(r,i,l){var a,c,f;if(this.supabaseUrl=r,this.supabaseKey=i,!r)throw new Error("supabaseUrl is required.");if(!i)throw new Error("supabaseKey is required.");const h=jv(r),p=new URL(h);this.realtimeUrl=new URL("realtime/v1",p),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",p),this.storageUrl=new URL("storage/v1",p),this.functionsUrl=new URL("functions/v1",p);const g=`sb-${p.hostname.split(".")[0]}-auth-token`,v={db:wv,realtime:kv,auth:Object.assign(Object.assign({},_v),{storageKey:g}),global:yv},w=Tv(l??{},v);this.storageKey=(a=w.auth.storageKey)!==null&&a!==void 0?a:"",this.headers=(c=w.global.headers)!==null&&c!==void 0?c:{},w.accessToken?(this.accessToken=w.accessToken,this.auth=new Proxy({},{get:(S,j)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(j)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((f=w.auth)!==null&&f!==void 0?f:{},this.headers,w.global.fetch),this.fetch=Cv(i,this._getAccessToken.bind(this),w.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},w.realtime)),this.rest=new Fg(new URL("rest/v1",p).href,{headers:this.headers,schema:w.db.schema,fetch:this.fetch}),this.storage=new mv(this.storageUrl.href,this.headers,this.fetch,l?.storage),w.accessToken||this._listenForAuthEvents()}get functions(){return new Rg(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}from(r){return this.rest.from(r)}schema(r){return this.rest.schema(r)}rpc(r,i={},l={}){return this.rest.rpc(r,i,l)}channel(r,i={config:{}}){return this.realtime.channel(r,i)}getChannels(){return this.realtime.getChannels()}removeChannel(r){return this.realtime.removeChannel(r)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var r,i;return Sy(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:l}=yield this.auth.getSession();return(i=(r=l.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:null})}_initSupabaseAuthClient({autoRefreshToken:r,persistSession:i,detectSessionInUrl:l,storage:a,storageKey:c,flowType:f,lock:h,debug:p},g,v){const w={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new ky({url:this.authUrl.href,headers:Object.assign(Object.assign({},w),g),storageKey:c,autoRefreshToken:r,persistSession:i,detectSessionInUrl:l,storage:a,flowType:f,lock:h,debug:p,fetch:v,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(r){return new nv(this.realtimeUrl.href,Object.assign(Object.assign({},r),{params:Object.assign({apikey:this.supabaseKey},r?.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((i,l)=>{this._handleTokenChanged(i,"CLIENT",l?.access_token)})}_handleTokenChanged(r,i,l){(r==="TOKEN_REFRESHED"||r==="SIGNED_IN")&&this.changedAccessToken!==l?this.changedAccessToken=l:r==="SIGNED_OUT"&&(this.realtime.setAuth(),i=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const xy=(o,r,i)=>new Ey(o,r,i);function Cy(){if(typeof window<"u"||typeof process>"u"||process.version===void 0||process.version===null)return!1;const o=process.version.match(/^v(\d+)\./);return o?parseInt(o[1],10)<=18:!1}Cy()&&console.warn("⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. Please upgrade to Node.js 20 or later. For more information, visit: https://github.com/orgs/supabase/discussions/37217");const Py="https://ckjpejxjpcfmlyopqabt.supabase.co",jy="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNranBlanhqcGNmbWx5b3BxYWJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MTk5OTUsImV4cCI6MjA2OTI5NTk5NX0.sdinJPYznrITCIJBijRV2iwA0TSLLsTmLWtTYY37OLE",at=xy(Py,jy),_i=({post:o,featured:r=!1})=>{const i=c=>new Date(c).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),l=(c,f=150)=>{if(!c)return"";const h=c.replace(/<[^>]*>/g,"").replace(/\n+/g," ").trim();if(h.length<=f)return h;const p=h.substring(0,f),g=p.lastIndexOf(" ");return g>0?p.substring(0,g)+"...":p+"..."},a=c=>"Admin";return r?L.jsxs(tt,{to:`/${o.slug}`,className:"featured",children:[L.jsx("div",{className:"featured-title",children:o.title}),L.jsx("div",{className:"featured-content",children:l(o.content,200)}),L.jsxs("div",{className:"featured-author",children:["By ",a(o.author_id)]})]}):L.jsxs(tt,{to:`/${o.slug}`,className:"poem-card",children:[L.jsx("div",{className:"poem-title",children:o.title}),L.jsx("div",{className:"poem-preview",children:l(o.content)}),L.jsxs("div",{className:"poem-meta",children:[L.jsxs("div",{className:"author",children:["By ",a(o.author_id)]}),L.jsx("div",{className:"date",children:i(o.published_at)})]})]})},Ty=({searchQuery:o})=>{const[r,i]=R.useState([]),[l,a]=R.useState(!0),[c,f]=R.useState(null),[h,p]=R.useState(!0),[g,v]=R.useState(0),w=12;R.useEffect(()=>{S(!0)},[o]);const S=async($=!1)=>{try{a(!0);const N=($?0:g)*w,J=N+w-1;let D=at.from("posts").select("id, title, slug, content, excerpt, author_id, published_at, status").eq("status","published").order("published_at",{ascending:!1}).range(N,J);o&&o.trim()&&(D=D.or(`title.ilike.%${o}%,content.ilike.%${o}%`));const{data:W,error:G}=await D;if(G)throw G;const ee=W||[];$?(i(ee),v(1)):(i(re=>[...re,...ee]),v(re=>re+1)),p(ee.length===w)}catch(E){console.error("Error fetching posts:",E),f("Failed to load posts")}finally{a(!1)}};if(l&&r.length===0)return L.jsx("div",{className:"main-grid",children:L.jsxs("div",{className:"loading",children:[L.jsx("div",{className:"spinner"}),"Loading posts..."]})});if(c)return L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"error",children:c})});if(r.length===0&&!l)return L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"loading",children:o?`No posts found for "${o}"`:"No posts available"})});const[j,...O]=r;return L.jsxs(L.Fragment,{children:[L.jsxs("div",{className:"main-grid",children:[j&&!o&&L.jsx(_i,{post:j,featured:!0}),(o?r:O).map($=>L.jsx(_i,{post:$},$.id))]}),h&&!o&&L.jsx("div",{className:"load-more-container",children:L.jsx("button",{onClick:()=>S(!1),disabled:l,className:"load-more-btn",children:l?L.jsxs("div",{className:"loading-inline",children:[L.jsx("div",{className:"spinner",style:{width:"16px",height:"16px",marginRight:"8px"}}),"Loading..."]}):"Load More Posts"})})]})},Ry=()=>{const{slug:o}=Js(),[r,i]=R.useState(null),[l,a]=R.useState([]),[c,f]=R.useState([]),[h,p]=R.useState([]),[g,v]=R.useState(!0),[w,S]=R.useState(null);R.useEffect(()=>{o&&j()},[o]);const j=async()=>{try{v(!0);const{data:E,error:N}=await at.from("posts").select("id, title, slug, content, author_id, published_at, status").eq("slug",o).eq("status","published").single();if(N)throw N;i(E);const[J,D,W]=await Promise.all([at.from("posts").select("id, title, slug, published_at").eq("status","published").neq("id",E.id).order("published_at",{ascending:!1}).limit(3),at.from("post_categories").select(`
            categories (
              id,
              name,
              slug
            )
          `).eq("post_id",E.id),at.from("post_tags").select(`
            tags (
              id,
              name,
              slug
            )
          `).eq("post_id",E.id)]);a(J.data||[]),f(D.data?.map(G=>G.categories)||[]),p(W.data?.map(G=>G.tags)||[])}catch(E){console.error("Error fetching post:",E),S("Post not found")}finally{v(!1)}},O=E=>new Date(E).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),$=E=>{if(!E)return"";let N=E.replace(/<!-- wp:.*? -->/g,"");N=N.replace(/<!-- \/wp:.*? -->/g,""),N=N.replace(/<p>/g,`

`),N=N.replace(/<\/p>/g,""),N=N.replace(/<br\s*\/?>/g,`
`),N=N.replace(/<[^>]*>/g,"");const J=document.createElement("textarea");return J.innerHTML=N,N=J.value,N.trim()};return g?L.jsx("div",{className:"single-poem-grid",children:L.jsx("div",{className:"loading",children:"Loading post..."})}):w||!r?L.jsx("div",{className:"single-poem-grid",children:L.jsx("div",{className:"error",children:w||"Post not found"})}):L.jsxs("div",{className:"single-poem-grid",children:[L.jsxs("div",{className:"poem-content",children:[L.jsx("div",{className:"poem-full-title",children:r.title}),L.jsxs("div",{className:"poem-meta",style:{marginBottom:"30px"},children:[L.jsx("div",{className:"author",children:"By Admin"}),L.jsx("div",{className:"date",style:{marginLeft:"20px"},children:O(r.published_at)})]}),L.jsx("div",{className:"poem-text",children:$(r.content)})]}),L.jsxs("div",{className:"poem-sidebar",children:[c.length>0&&L.jsxs("div",{className:"sidebar-section",children:[L.jsx("div",{className:"sidebar-title",children:"Categories"}),L.jsx("div",{className:"sidebar-content",children:c.map((E,N)=>L.jsxs("span",{children:[L.jsx(tt,{to:`/category/${E.slug}`,style:{color:"#666",textDecoration:"none"},children:E.name}),N<c.length-1&&", "]},E.id))})]}),h.length>0&&L.jsxs("div",{className:"sidebar-section",children:[L.jsx("div",{className:"sidebar-title",children:"Tags"}),L.jsx("div",{className:"sidebar-content",children:h.map((E,N)=>L.jsxs("span",{children:[L.jsx(tt,{to:`/tag/${E.slug}`,style:{color:"#666",textDecoration:"none"},children:E.name}),N<h.length-1&&", "]},E.id))})]}),l.length>0&&L.jsxs("div",{className:"sidebar-section",children:[L.jsx("div",{className:"sidebar-title",children:"Related Posts"}),L.jsx("div",{className:"sidebar-content",children:l.map(E=>L.jsx("div",{style:{marginBottom:"10px"},children:L.jsx(tt,{to:`/${E.slug}`,style:{color:"#666",textDecoration:"none",fontSize:"13px",display:"block"},children:E.title})},E.id))})]})]})]})},Oy=({searchQuery:o})=>{const[r,i]=R.useState([]),[l,a]=R.useState(!0),[c,f]=R.useState(null);R.useEffect(()=>{h()},[o]);const h=async()=>{try{a(!0);let v=`
        SELECT
          u.id, u.wp_id, u.user_login, u.display_name, u.user_registered,
          COUNT(p.id) as post_count
        FROM users u
        LEFT JOIN posts p ON u.id = p.author_id AND p.status = 'published'
      `;o&&o.trim()&&(v+=` WHERE u.display_name ILIKE '%${o}%'`),v+=`
        GROUP BY u.id, u.wp_id, u.user_login, u.display_name, u.user_registered
        HAVING COUNT(p.id) > 0
        ORDER BY COUNT(p.id) DESC
      `;const{data:w,error:S}=await at.rpc("execute_sql",{query:v});if(S)return console.warn("RPC query failed, using fallback method:",S),await p();const j=w?.map(O=>({id:O.id,wp_id:O.wp_id,user_login:O.user_login,display_name:O.display_name,user_registered:O.user_registered,postCount:parseInt(O.post_count)||0}))||[];i(j)}catch(v){console.error("Error fetching authors:",v),await p()}finally{a(!1)}},p=async()=>{try{const{data:v,error:w}=await at.from("posts").select(`
          author_id,
          users!posts_author_id_fkey (
            id,
            wp_id,
            user_login,
            display_name,
            user_registered
          )
        `).eq("status","published");if(w)throw w;const S=new Map;v?.forEach(O=>{const $=O.users;if($){const E=$.id;S.has(E)?S.get(E).postCount++:S.set(E,{...$,postCount:1})}});let j=Array.from(S.values());if(o&&o.trim()){const O=o.toLowerCase();j=j.filter($=>$.display_name.toLowerCase().includes(O))}j.sort((O,$)=>$.postCount-O.postCount),i(j)}catch(v){console.error("Error in fallback method:",v),f("Failed to load authors")}},g=v=>v?v.split(" ").map(w=>w.charAt(0)).join("").toUpperCase().substring(0,2):"A";return l?L.jsx("div",{className:"author-grid",children:L.jsx("div",{className:"loading",children:"Loading authors..."})}):c?L.jsx("div",{className:"author-grid",children:L.jsx("div",{className:"error",children:c})}):r.length===0?L.jsx("div",{className:"author-grid",children:L.jsx("div",{className:"loading",children:o?`No authors found for "${o}"`:"No authors available"})}):L.jsx("div",{className:"author-grid",children:r.map(v=>L.jsxs(tt,{to:`/author/${v.user_login}`,className:"author-card",children:[L.jsx("div",{className:"author-avatar",children:g(v.display_name)}),L.jsx("div",{className:"author-name",children:v.display_name}),L.jsxs("div",{className:"author-count",children:[v.postCount," ",v.postCount===1?"post":"posts"]})]},v.id))})},Ny=({searchQuery:o})=>{const{username:r}=Js(),[i,l]=R.useState([]),[a,c]=R.useState(null),[f,h]=R.useState(!0),[p,g]=R.useState(null);R.useEffect(()=>{r&&v()},[r,o]);const v=async()=>{try{h(!0);const{data:j,error:O}=await at.from("users").select("*").eq("user_login",r).single();if(O)throw O;c(j);let $=at.from("posts").select("*").eq("author_id",j.id).eq("status","published").order("published_at",{ascending:!1});o&&o.trim()&&($=$.or(`title.ilike.%${o}%,content.ilike.%${o}%`));const{data:E,error:N}=await $;if(N)throw N;l(E||[])}catch(j){console.error("Error fetching author posts:",j),g("Author not found")}finally{h(!1)}},w=j=>j?j.split(" ").map(O=>O.charAt(0)).join("").toUpperCase().substring(0,2):"A",S=j=>new Date(j).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return f?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"loading",children:"Loading author..."})}):p||!a?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"error",children:p||"Author not found"})}):L.jsxs(L.Fragment,{children:[a&&L.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"40px 20px",background:"white",border:"1px solid #f0f0f0"},children:[L.jsx("div",{style:{width:"80px",height:"80px",background:"#ddd",borderRadius:"50%",margin:"0 auto 20px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"32px",fontWeight:"bold",color:"#666"},children:w(a.display_name)}),L.jsx("h1",{style:{fontSize:"32px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:a.display_name}),L.jsxs("p",{style:{color:"#666",fontSize:"16px",marginBottom:"10px"},children:["@",a.user_login]}),L.jsxs("p",{style:{color:"#999",fontSize:"14px",marginBottom:"10px"},children:["Member since ",S(a.user_registered)]}),L.jsxs("p",{style:{color:"#999",fontSize:"14px"},children:[i.length," ",i.length===1?"post":"posts"," published"]})]}),i.length===0?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"loading",children:o?`No posts found by ${a?.display_name} for "${o}"`:`No posts found by ${a?.display_name}`})}):L.jsx("div",{className:"main-grid",children:i.map(j=>L.jsx(_i,{post:j},j.id))})]})},Ly=({searchQuery:o})=>{const{slug:r}=Js(),[i,l]=R.useState([]),[a,c]=R.useState(null),[f,h]=R.useState(!0),[p,g]=R.useState(null);R.useEffect(()=>{r&&v()},[r,o]);const v=async()=>{try{h(!0);const{data:w,error:S}=await at.from("categories").select("id, name, slug, description").eq("slug",r).single();if(S)throw S;c(w);const{data:j,error:O}=await at.from("post_categories").select(`
          posts (
            id,
            title,
            slug,
            content,
            excerpt,
            author_id,
            published_at
          )
        `).eq("category_id",w.id);if(O)throw O;let $=j?.map(E=>E.posts).filter(E=>E)||[];if(o&&o.trim()){const E=o.toLowerCase();$=$.filter(N=>N.title.toLowerCase().includes(E)||N.content&&N.content.toLowerCase().includes(E))}$.sort((E,N)=>new Date(N.published_at)-new Date(E.published_at)),l($)}catch(w){console.error("Error fetching category posts:",w),g("Failed to load category posts")}finally{h(!1)}};return f?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"loading",children:"Loading posts..."})}):p?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"error",children:p})}):i.length===0?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"loading",children:o?`No posts found in "${a?.name}" for "${o}"`:`No posts found in "${a?.name}"`})}):L.jsxs(L.Fragment,{children:[a&&L.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"20px",background:"white",border:"1px solid #f0f0f0"},children:[L.jsx("h1",{style:{fontSize:"28px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:a.name}),a.description&&L.jsx("p",{style:{color:"#666",fontSize:"16px"},children:a.description}),L.jsxs("p",{style:{color:"#999",fontSize:"14px",marginTop:"10px"},children:[i.length," ",i.length===1?"post":"posts"]})]}),L.jsx("div",{className:"main-grid",children:i.map(w=>L.jsx(_i,{post:w},w.id))})]})},$y=({searchQuery:o})=>{const{slug:r}=Js(),[i,l]=R.useState([]),[a,c]=R.useState(null),[f,h]=R.useState(!0),[p,g]=R.useState(null);R.useEffect(()=>{r&&v()},[r,o]);const v=async()=>{try{h(!0);const{data:w,error:S}=await at.from("tags").select("id, name, slug, description").eq("slug",r).single();if(S)throw S;c(w);const{data:j,error:O}=await at.from("post_tags").select(`
          posts (
            id,
            title,
            slug,
            content,
            excerpt,
            author_id,
            published_at
          )
        `).eq("tag_id",w.id);if(O)throw O;let $=j?.map(E=>E.posts).filter(E=>E)||[];if(o&&o.trim()){const E=o.toLowerCase();$=$.filter(N=>N.title.toLowerCase().includes(E)||N.content&&N.content.toLowerCase().includes(E))}$.sort((E,N)=>new Date(N.published_at)-new Date(E.published_at)),l($)}catch(w){console.error("Error fetching tag posts:",w),g("Failed to load tag posts")}finally{h(!1)}};return f?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"loading",children:"Loading posts..."})}):p?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"error",children:p})}):i.length===0?L.jsx("div",{className:"main-grid",children:L.jsx("div",{className:"loading",children:o?`No posts found with tag "${a?.name}" for "${o}"`:`No posts found with tag "${a?.name}"`})}):L.jsxs(L.Fragment,{children:[a&&L.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"20px",background:"white",border:"1px solid #f0f0f0"},children:[L.jsxs("h1",{style:{fontSize:"28px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:["#",a.name]}),a.description&&L.jsx("p",{style:{color:"#666",fontSize:"16px"},children:a.description}),L.jsxs("p",{style:{color:"#999",fontSize:"14px",marginTop:"10px"},children:[i.length," ",i.length===1?"post":"posts"]})]}),L.jsx("div",{className:"main-grid",children:i.map(w=>L.jsx(_i,{post:w},w.id))})]})};function Iy(){const[o,r]=R.useState(""),i=l=>{r(l)};return L.jsx(fg,{children:L.jsxs("div",{className:"container",children:[L.jsx(Sg,{onSearch:i,searchQuery:o,setSearchQuery:r}),L.jsxs(Wm,{children:[L.jsx(An,{path:"/",element:L.jsx(Ty,{searchQuery:o})}),L.jsx(An,{path:"/authors",element:L.jsx(Oy,{searchQuery:o})}),L.jsx(An,{path:"/author/:username",element:L.jsx(Ny,{searchQuery:o})}),L.jsx(An,{path:"/category/:slug",element:L.jsx(Ly,{searchQuery:o})}),L.jsx(An,{path:"/tag/:slug",element:L.jsx($y,{searchQuery:o})}),L.jsx(An,{path:"/:slug",element:L.jsx(Ry,{})})]}),L.jsx(Eg,{})]})})}Xp.createRoot(document.getElementById("root")).render(L.jsx(R.StrictMode,{children:L.jsx(Iy,{})}));
